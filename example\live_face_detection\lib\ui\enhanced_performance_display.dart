// Enhanced Performance Display Widget
import 'package:flutter/material.dart';
import 'package:live_face_detection/service/detailed_performance_tracker.dart';

/// Enhanced performance display with detailed metrics
class EnhancedPerformanceDisplay extends StatefulWidget {
  final String currentModelName;
  final int currentFaceCount;

  const EnhancedPerformanceDisplay({
    Key? key,
    required this.currentModelName,
    required this.currentFaceCount,
  }) : super(key: key);

  @override
  State<EnhancedPerformanceDisplay> createState() => _EnhancedPerformanceDisplayState();
}

class _EnhancedPerformanceDisplayState extends State<EnhancedPerformanceDisplay> {
  PerformanceSnapshot? _lastSnapshot;
  
  @override
  void initState() {
    super.initState();
    // Update performance display every 500ms
    Stream.periodic(const Duration(milliseconds: 500)).listen((_) {
      if (mounted) {
        setState(() {
          _lastSnapshot = DetailedPerformanceTracker.getCurrentSnapshot();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final snapshot = _lastSnapshot ?? DetailedPerformanceTracker.getCurrentSnapshot();
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with model name
          Row(
            children: [
              Icon(Icons.speed, color: Colors.green, size: 16),
              const SizedBox(width: 6),
              Text(
                widget.currentModelName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Faces: ${widget.currentFaceCount}',
                  style: const TextStyle(
                    color: Colors.green,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // FPS Metrics Row
          Row(
            children: [
              _buildMetricChip(
                'Camera FPS', 
                '${snapshot.cameraFPS.toStringAsFixed(1)}',
                Colors.blue,
              ),
              const SizedBox(width: 8),
              _buildMetricChip(
                'Process FPS', 
                '${snapshot.processingFPS.toStringAsFixed(1)}',
                Colors.orange,
              ),
              const SizedBox(width: 8),
              _buildMetricChip(
                'Ratio', 
                '${(snapshot.processingRatio * 100).toStringAsFixed(0)}%',
                snapshot.processingRatio > 0.8 ? Colors.green : 
                snapshot.processingRatio > 0.5 ? Colors.orange : Colors.red,
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Pipeline Breakdown
          _buildPipelineBreakdown(snapshot),
          
          const SizedBox(height: 8),
          
          // Bottleneck Indicator
          _buildBottleneckIndicator(snapshot),
        ],
      ),
    );
  }

  Widget _buildMetricChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.5), width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 8,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPipelineBreakdown(PerformanceSnapshot snapshot) {
    final steps = [
      ('Conv', snapshot.avgImageConversionTime, Colors.purple),
      ('Resize', snapshot.avgImageResizeTime, Colors.blue),
      ('Tensor', snapshot.avgTensorCreationTime, Colors.cyan),
      ('Infer', snapshot.avgInferenceTime, Colors.red),
      ('Post', snapshot.avgPostProcessingTime, Colors.green),
    ];
    
    final totalTime = snapshot.avgTotalPipelineTime;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.timeline, color: Colors.white70, size: 12),
            const SizedBox(width: 4),
            Text(
              'Pipeline: ${totalTime.toStringAsFixed(0)}ms',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        
        // Pipeline bar
        Container(
          height: 20,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.white30, width: 1),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(3),
            child: Row(
              children: steps.map((step) {
                final percentage = totalTime > 0 ? step.$2 / totalTime : 0.0;
                return Expanded(
                  flex: (percentage * 100).round().clamp(1, 100),
                  child: Container(
                    color: step.$3.withOpacity(0.7),
                    child: Center(
                      child: Text(
                        step.$1,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        
        const SizedBox(height: 4),
        
        // Step timings
        Wrap(
          spacing: 4,
          runSpacing: 2,
          children: steps.map((step) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
              decoration: BoxDecoration(
                color: step.$3.withOpacity(0.2),
                borderRadius: BorderRadius.circular(2),
              ),
              child: Text(
                '${step.$1}: ${step.$2.toStringAsFixed(0)}ms',
                style: TextStyle(
                  color: step.$3,
                  fontSize: 8,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildBottleneckIndicator(PerformanceSnapshot snapshot) {
    // Find the biggest bottleneck
    final steps = [
      ('Image Conversion', snapshot.avgImageConversionTime),
      ('Image Resize', snapshot.avgImageResizeTime),
      ('Tensor Creation', snapshot.avgTensorCreationTime),
      ('Model Inference', snapshot.avgInferenceTime),
      ('Post Processing', snapshot.avgPostProcessingTime),
    ];
    
    steps.sort((a, b) => b.$2.compareTo(a.$2));
    final bottleneck = steps.first;
    final percentage = snapshot.avgTotalPipelineTime > 0 
      ? (bottleneck.$2 / snapshot.avgTotalPipelineTime) * 100 
      : 0.0;
    
    Color indicatorColor;
    String severityText;
    
    if (percentage > 50) {
      indicatorColor = Colors.red;
      severityText = 'CRITICAL';
    } else if (percentage > 35) {
      indicatorColor = Colors.orange;
      severityText = 'HIGH';
    } else if (percentage > 20) {
      indicatorColor = Colors.yellow;
      severityText = 'MEDIUM';
    } else {
      indicatorColor = Colors.green;
      severityText = 'LOW';
    }
    
    return Row(
      children: [
        Icon(Icons.warning, color: indicatorColor, size: 12),
        const SizedBox(width: 4),
        Text(
          'Bottleneck: ${bottleneck.$1}',
          style: TextStyle(
            color: indicatorColor,
            fontSize: 9,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
          decoration: BoxDecoration(
            color: indicatorColor.withOpacity(0.2),
            borderRadius: BorderRadius.circular(2),
          ),
          child: Text(
            severityText,
            style: TextStyle(
              color: indicatorColor,
              fontSize: 7,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const Spacer(),
        Text(
          '${percentage.toStringAsFixed(0)}%',
          style: TextStyle(
            color: indicatorColor,
            fontSize: 9,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}

/// Compact performance display for overlay
class CompactPerformanceDisplay extends StatelessWidget {
  final String modelName;
  final int faceCount;
  final double cameraFPS;
  final double processingFPS;
  final int inferenceTime;

  const CompactPerformanceDisplay({
    Key? key,
    required this.modelName,
    required this.faceCount,
    required this.cameraFPS,
    required this.processingFPS,
    required this.inferenceTime,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            modelName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'Cam: ${cameraFPS.toStringAsFixed(1)} fps',
            style: const TextStyle(
              color: Colors.blue,
              fontSize: 9,
            ),
          ),
          Text(
            'Proc: ${processingFPS.toStringAsFixed(1)} fps',
            style: const TextStyle(
              color: Colors.orange,
              fontSize: 9,
            ),
          ),
          Text(
            'Infer: ${inferenceTime}ms',
            style: const TextStyle(
              color: Colors.red,
              fontSize: 9,
            ),
          ),
          Text(
            'Faces: $faceCount',
            style: const TextStyle(
              color: Colors.green,
              fontSize: 9,
            ),
          ),
        ],
      ),
    );
  }
}
