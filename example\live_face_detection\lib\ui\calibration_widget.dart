// Device Calibration UI Widget
import 'package:flutter/material.dart';
import 'package:live_face_detection/service/device_calibration_service.dart';

/// Widget for device-specific coordinate calibration
class CalibrationWidget extends StatefulWidget {
  final VoidCallback? onCalibrationChanged;

  const CalibrationWidget({
    Key? key,
    this.onCalibrationChanged,
  }) : super(key: key);

  @override
  State<CalibrationWidget> createState() => _CalibrationWidgetState();
}

class _CalibrationWidgetState extends State<CalibrationWidget> {
  final _calibrationService = DeviceCalibrationService.instance;
  
  double _scaleFactorX = 1.0;
  double _scaleFactorY = 1.0;
  double _offsetX = 0.0;
  double _offsetY = 0.0;
  double _rotation = -90.0; // Rotation in degrees - Default -90° to align red box with screen

  // Text controllers for manual input
  final TextEditingController _scaleXController = TextEditingController();
  final TextEditingController _scaleYController = TextEditingController();
  final TextEditingController _offsetXController = TextEditingController();
  final TextEditingController _offsetYController = TextEditingController();
  final TextEditingController _rotationController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCurrentCalibration();
    _initializeControllers();
  }

  void _initializeControllers() {
    _scaleXController.text = _scaleFactorX.toStringAsFixed(2);
    _scaleYController.text = _scaleFactorY.toStringAsFixed(2);
    _offsetXController.text = _offsetX.toStringAsFixed(0);
    _offsetYController.text = _offsetY.toStringAsFixed(0);
    _rotationController.text = _rotation.toStringAsFixed(1);
  }

  void _updateControllers() {
    _scaleXController.text = _scaleFactorX.toStringAsFixed(2);
    _scaleYController.text = _scaleFactorY.toStringAsFixed(2);
    _offsetXController.text = _offsetX.toStringAsFixed(0);
    _offsetYController.text = _offsetY.toStringAsFixed(0);
    _rotationController.text = _rotation.toStringAsFixed(1);
  }

  @override
  void dispose() {
    _scaleXController.dispose();
    _scaleYController.dispose();
    _offsetXController.dispose();
    _offsetYController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  void _loadCurrentCalibration() {
    final profile = _calibrationService.currentProfile;
    if (profile != null) {
      setState(() {
        _scaleFactorX = profile.scaleFactorX;
        _scaleFactorY = profile.scaleFactorY;
        _offsetX = profile.offsetX;
        _offsetY = profile.offsetY;
        _rotation = profile.rotation;
      });
      _updateControllers();
    }
  }

  Future<void> _updateCalibration() async {
    await _calibrationService.updateCalibration(
      scaleFactorX: _scaleFactorX,
      scaleFactorY: _scaleFactorY,
      offsetX: _offsetX,
      offsetY: _offsetY,
      rotation: _rotation,
    );
    widget.onCalibrationChanged?.call();
  }

  Future<void> _resetCalibration() async {
    await _calibrationService.resetCalibration();
    setState(() {
      _scaleFactorX = 1.0;
      _scaleFactorY = 1.0;
      _offsetX = 0.0;
      _offsetY = 0.0;
      _rotation = -90.0; // Default -90° to align red box with screen
    });
    _updateControllers();
    widget.onCalibrationChanged?.call();
  }

  @override
  Widget build(BuildContext context) {
    final profile = _calibrationService.currentProfile;
    
    return Container(
      height: MediaQuery.of(context).size.height * 0.7, // Giới hạn chiều cao 70% màn hình
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange, width: 2),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          // Header
          Row(
            children: [
              const Icon(Icons.tune, color: Colors.blue, size: 24),
              const SizedBox(width: 8),
              const Text(
                'Coordinate Calibration',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.refresh, color: Colors.orange),
                onPressed: _resetCalibration,
                tooltip: 'Reset to defaults',
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Device Info
          if (profile != null) ...[
            _buildInfoCard(
              'Device Information',
              [
                'Model: ${profile.deviceModel}',
                'Screen: ${profile.screenSize.width.toInt()}×${profile.screenSize.height.toInt()}',
                'Orientation: ${profile.orientation.name}',
                'Pixel Ratio: ${profile.pixelRatio.toStringAsFixed(1)}',
              ],
            ),
            
            const SizedBox(height: 12),
          ],

          // Scale Factors
          _buildCalibrationSection(
            'Scale Factors',
            [
              _buildInputField(
                'Horizontal Scale',
                _scaleXController,
                'x',
                (value) {
                  setState(() => _scaleFactorX = value);
                  _scaleXController.text = value.toStringAsFixed(2);
                  _updateCalibration();
                },
              ),
              _buildInputField(
                'Vertical Scale',
                _scaleYController,
                'x',
                (value) {
                  setState(() => _scaleFactorY = value);
                  _scaleYController.text = value.toStringAsFixed(2);
                  _updateCalibration();
                },
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Offset Adjustments
          _buildCalibrationSection(
            'Position Offsets',
            [
              _buildInputField(
                'Horizontal Offset',
                _offsetXController,
                'px',
                (value) {
                  setState(() => _offsetX = value);
                  _offsetXController.text = value.toStringAsFixed(0);
                  _updateCalibration();
                },
              ),
              _buildInputField(
                'Vertical Offset',
                _offsetYController,
                'px',
                (value) {
                  setState(() => _offsetY = value);
                  _offsetYController.text = value.toStringAsFixed(0);
                  _updateCalibration();
                },
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Rotation Adjustment
          _buildCalibrationSection(
            'Rotation',
            [
              _buildInputField(
                'Rotation Angle',
                _rotationController,
                '°',
                (value) {
                  setState(() => _rotation = value);
                  _rotationController.text = value.toStringAsFixed(1);
                  _updateCalibration();
                },
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Calibration Status
          _buildStatusCard(),

          const SizedBox(height: 12),

          // Instructions
          _buildInstructionsCard(),
        ],
      ),
      ), // Đóng SingleChildScrollView
    );
  }

  Widget _buildInfoCard(String title, List<String> items) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...items.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              item,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildCalibrationSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  Widget _buildSlider(
    String label,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
    String displayValue,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
              Text(
                displayValue,
                style: const TextStyle(
                  color: Colors.blue,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: ((max - min) * 20).round(),
            activeColor: Colors.blue,
            inactiveColor: Colors.grey[700],
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildInputField(
    String label,
    TextEditingController controller,
    String suffix,
    ValueChanged<double> onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  style: const TextStyle(color: Colors.white),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Colors.grey[800],
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Colors.blue, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    suffixText: suffix,
                    suffixStyle: const TextStyle(color: Colors.blue),
                  ),
                  onSubmitted: (value) {
                    final doubleValue = double.tryParse(value);
                    if (doubleValue != null) {
                      onChanged(doubleValue);
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () {
                  final doubleValue = double.tryParse(controller.text);
                  if (doubleValue != null) {
                    onChanged(doubleValue);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  minimumSize: const Size(50, 36),
                ),
                child: const Text('Set', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCard() {
    final isCalibrated = _calibrationService.isCalibrated;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isCalibrated ? Colors.green[900] : Colors.orange[900],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            isCalibrated ? Icons.check_circle : Icons.warning,
            color: isCalibrated ? Colors.green : Colors.orange,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            isCalibrated ? 'Device Calibrated' : 'Using Default Settings',
            style: TextStyle(
              color: isCalibrated ? Colors.green[100] : Colors.orange[100],
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionsCard() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[900]?.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.info, color: Colors.blue, size: 16),
              SizedBox(width: 8),
              Text(
                'Calibration Instructions',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            '• Adjust scale factors if bounding boxes are too large/small\n'
            '• Use position offsets to align boxes with faces\n'
            '• Test with different face positions and distances\n'
            '• Settings are saved automatically per device orientation',
            style: TextStyle(
              color: Colors.blue,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
