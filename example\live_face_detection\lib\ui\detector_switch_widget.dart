// Detector Switch Widget - Quick switching between face detection models
import 'package:flutter/material.dart';
import 'package:live_face_detection/models/face_detection_model.dart';

/// Widget for quickly switching between face detection models
class DetectorSwitchWidget extends StatelessWidget {
  final FaceDetectionModel currentModel;
  final Function(FaceDetectionModel) onModelChanged;
  final Map<FaceDetectionModel, bool> modelStatus;
  final Map<FaceDetectionModel, Map<String, dynamic>>? performanceData;

  const DetectorSwitchWidget({
    Key? key,
    required this.currentModel,
    required this.onModelChanged,
    required this.modelStatus,
    this.performanceData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.swap_horiz,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Detector Switch',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Model buttons
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: FaceDetectionModel.values.map((model) {
              final isActive = model == currentModel;
              final isAvailable = modelStatus[model] ?? false;
              final performance = performanceData?[model];
              
              return _buildModelButton(
                model: model,
                isActive: isActive,
                isAvailable: isAvailable,
                performance: performance,
                onTap: () => onModelChanged(model),
              );
            }).toList(),
          ),
          
          // Performance comparison
          if (performanceData != null && performanceData!.isNotEmpty)
            _buildPerformanceComparison(),
        ],
      ),
    );
  }

  /// Build individual model button
  Widget _buildModelButton({
    required FaceDetectionModel model,
    required bool isActive,
    required bool isAvailable,
    Map<String, dynamic>? performance,
    required VoidCallback onTap,
  }) {
    final specs = ModelSpecs.getSpecs(model);
    
    Color backgroundColor;
    Color textColor;
    Color borderColor;
    
    if (!isAvailable) {
      backgroundColor = Colors.grey.withOpacity(0.3);
      textColor = Colors.grey;
      borderColor = Colors.grey.withOpacity(0.5);
    } else if (isActive) {
      backgroundColor = Colors.blue.withOpacity(0.8);
      textColor = Colors.white;
      borderColor = Colors.blue;
    } else {
      backgroundColor = Colors.white.withOpacity(0.1);
      textColor = Colors.white;
      borderColor = Colors.white.withOpacity(0.3);
    }

    return GestureDetector(
      onTap: isAvailable ? onTap : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: borderColor),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Model name
            Text(
              model.displayName,
              style: TextStyle(
                color: textColor,
                fontSize: 12,
                fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            
            // Status indicator
            const SizedBox(height: 4),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: isAvailable ? Colors.green : Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  isAvailable ? 'Ready' : 'Failed',
                  style: TextStyle(
                    color: textColor.withOpacity(0.8),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
            
            // Performance info
            if (performance != null && isAvailable) ...[
              const SizedBox(height: 4),
              Text(
                '${performance['avgTime'] ?? 0}ms',
                style: TextStyle(
                  color: textColor.withOpacity(0.9),
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
            
            // Input size info (except MediaPipe)
            if (model != FaceDetectionModel.mediaPipe) ...[
              const SizedBox(height: 2),
              Text(
                '${specs.inputWidth}×${specs.inputHeight}',
                style: TextStyle(
                  color: textColor.withOpacity(0.7),
                  fontSize: 9,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build performance comparison section
  Widget _buildPerformanceComparison() {
    final sortedModels = FaceDetectionModel.values.where((model) {
      final data = performanceData![model];
      return data != null && (data['avgTime'] ?? 0) > 0;
    }).toList();

    if (sortedModels.isEmpty) return const SizedBox.shrink();

    // Sort by performance (fastest first)
    sortedModels.sort((a, b) {
      final aTime = performanceData![a]?['avgTime'] ?? 999999;
      final bTime = performanceData![b]?['avgTime'] ?? 999999;
      return aTime.compareTo(bTime);
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 12),
        Text(
          'Performance Ranking',
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...sortedModels.asMap().entries.map((entry) {
          final index = entry.key;
          final model = entry.value;
          final data = performanceData![model]!;
          final avgTime = data['avgTime'] ?? 0;
          final fps = data['fps'] ?? '0.0';
          
          String medal = '';
          Color rankColor = Colors.white;
          
          if (index == 0) {
            medal = '🥇';
            rankColor = Colors.amber;
          } else if (index == 1) {
            medal = '🥈';
            rankColor = Colors.grey[300]!;
          } else if (index == 2) {
            medal = '🥉';
            rankColor = Colors.orange[300]!;
          }

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Text(
                  medal.isNotEmpty ? medal : '${index + 1}.',
                  style: TextStyle(
                    color: rankColor,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    model.displayName,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 11,
                    ),
                  ),
                ),
                Text(
                  '${avgTime}ms',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${fps} FPS',
                  style: TextStyle(
                    color: Colors.green.withOpacity(0.8),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }
}

/// Compact detector switch for overlay
class CompactDetectorSwitch extends StatelessWidget {
  final FaceDetectionModel currentModel;
  final Function(FaceDetectionModel) onModelChanged;
  final Map<FaceDetectionModel, bool> modelStatus;

  const CompactDetectorSwitch({
    Key? key,
    required this.currentModel,
    required this.onModelChanged,
    required this.modelStatus,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final availableModels = FaceDetectionModel.values
        .where((model) => modelStatus[model] ?? false)
        .toList();

    if (availableModels.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.speed,
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(width: 6),
          ...availableModels.map((model) {
            final isActive = model == currentModel;
            return GestureDetector(
              onTap: () => onModelChanged(model),
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 2),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isActive ? Colors.blue.withOpacity(0.8) : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getShortName(model),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  String _getShortName(FaceDetectionModel model) {
    switch (model) {
      case FaceDetectionModel.blazeFace:
        return 'BF';
      case FaceDetectionModel.ultraFaceRFB:
        return 'UR';
      case FaceDetectionModel.ultraFaceSlim:
        return 'US';
      case FaceDetectionModel.ultraFaceSlimINT8:
        return 'U8';
      case FaceDetectionModel.mediaPipe:
        return 'MP';
    }
  }
}
