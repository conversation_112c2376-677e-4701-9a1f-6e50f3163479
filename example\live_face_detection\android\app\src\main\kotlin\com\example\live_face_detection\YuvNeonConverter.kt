package com.example.live_face_detection

import android.os.Build
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import java.io.BufferedReader
import java.io.FileReader

class YuvNeonConverter : MethodChannel.MethodCallHandler {
    
    companion object {
        init {
            try {
                System.loadLibrary("yuv_neon_converter")
            } catch (e: UnsatisfiedLinkError) {
                android.util.Log.e("YuvNeonConverter", "Failed to load native library: ${e.message}")
            }
        }
        
        private const val CHANNEL = "yuv_neon_converter"
        
        fun registerWith(flutterEngine: FlutterEngine) {
            val channel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
            channel.setMethodCallHandler(YuvNeonConverter())
        }
    }
    
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "convertYuv420ToRgb" -> {
                try {
                    val yPlane = call.argument<ByteArray>("yPlane")!!
                    val yStride = call.argument<Int>("yStride")!!
                    val uPlane = call.argument<ByteArray>("uPlane")!!
                    val uStride = call.argument<Int>("uStride")!!
                    val vPlane = call.argument<ByteArray>("vPlane")!!
                    val vStride = call.argument<Int>("vStride")!!
                    val rgbOutput = call.argument<ByteArray>("rgbOutput")!!
                    val rgbStride = call.argument<Int>("rgbStride")!!
                    val width = call.argument<Int>("width")!!
                    val height = call.argument<Int>("height")!!
                    
                    // Call native NEON conversion
                    convertYuv420ToRgb(
                        yPlane, yStride,
                        uPlane, uStride,
                        vPlane, vStride,
                        rgbOutput, rgbStride,
                        width, height
                    )
                    
                    result.success(rgbOutput)
                } catch (e: Exception) {
                    android.util.Log.e("YuvNeonConverter", "Conversion failed: ${e.message}")
                    result.error("CONVERSION_FAILED", e.message, null)
                }
            }
            
            "isNeonAvailable" -> {
                result.success(isNeonAvailable())
            }
            
            "getCpuInfo" -> {
                result.success(getCpuInfo())
            }
            
            else -> {
                result.notImplemented()
            }
        }
    }
    
    /**
     * Native method declaration for ARM NEON YUV conversion
     */
    private external fun convertYuv420ToRgb(
        yPlane: ByteArray, yStride: Int,
        uPlane: ByteArray, uStride: Int,
        vPlane: ByteArray, vStride: Int,
        rgbOutput: ByteArray, rgbStride: Int,
        width: Int, height: Int
    )
    
    /**
     * Check if ARM NEON is available on this device
     */
    private fun isNeonAvailable(): Boolean {
        return try {
            // Check CPU features for NEON support
            val cpuInfo = readCpuInfo()
            cpuInfo.contains("neon") || cpuInfo.contains("asimd")
        } catch (e: Exception) {
            // Fallback: assume NEON is available on ARM64 devices
            Build.SUPPORTED_64_BIT_ABIS.isNotEmpty()
        }
    }
    
    /**
     * Get detailed CPU information for optimization
     */
    private fun getCpuInfo(): Map<String, Any> {
        val cpuInfo = mutableMapOf<String, Any>()
        
        try {
            // Basic device info
            cpuInfo["device"] = Build.DEVICE
            cpuInfo["model"] = Build.MODEL
            cpuInfo["manufacturer"] = Build.MANUFACTURER
            cpuInfo["hardware"] = Build.HARDWARE
            
            // CPU architecture
            cpuInfo["supportedAbis"] = Build.SUPPORTED_ABIS.toList()
            cpuInfo["supported32BitAbis"] = Build.SUPPORTED_32_BIT_ABIS.toList()
            cpuInfo["supported64BitAbis"] = Build.SUPPORTED_64_BIT_ABIS.toList()
            
            // CPU features from /proc/cpuinfo
            val cpuFeatures = readCpuInfo()
            cpuInfo["cpuFeatures"] = cpuFeatures
            cpuInfo["hasNeon"] = cpuFeatures.contains("neon") || cpuFeatures.contains("asimd")
            cpuInfo["hasVfp"] = cpuFeatures.contains("vfp")
            
            // Check for RK3399 specifically
            cpuInfo["isRK3399"] = cpuFeatures.contains("rk3399") || 
                                  Build.HARDWARE.contains("rk3399", ignoreCase = true) ||
                                  Build.DEVICE.contains("rk3399", ignoreCase = true)
            
            // CPU core count
            cpuInfo["coreCount"] = Runtime.getRuntime().availableProcessors()
            
        } catch (e: Exception) {
            android.util.Log.e("YuvNeonConverter", "Failed to get CPU info: ${e.message}")
            cpuInfo["error"] = e.message ?: "Unknown error"
        }
        
        return cpuInfo
    }
    
    /**
     * Read CPU information from /proc/cpuinfo
     */
    private fun readCpuInfo(): String {
        return try {
            val reader = BufferedReader(FileReader("/proc/cpuinfo"))
            val cpuInfo = reader.use { it.readText() }
            cpuInfo.lowercase()
        } catch (e: Exception) {
            android.util.Log.w("YuvNeonConverter", "Could not read /proc/cpuinfo: ${e.message}")
            ""
        }
    }
}
