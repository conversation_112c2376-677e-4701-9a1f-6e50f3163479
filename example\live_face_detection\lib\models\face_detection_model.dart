/// Face Detection Model Types
enum FaceDetectionModel {
  blazeFace('BlazeFace', 'assets/civams_models/blazeface.tfl'),
  ultraFaceRFB('UltraFace RFB', 'assets/civams_models/ultraface_rfb.tflite'),
  ultraFaceSlim('UltraFace Slim', 'assets/civams_models/ultraface_slim.tflite'),
  ultraFaceSlimINT8('UltraFace Slim INT8', 'assets/civams_models/rfb_slim_int8.tflite'),
  mediaPipe('MediaPipe', 'google_ml_kit'); // No model file needed

  const FaceDetectionModel(this.displayName, this.modelPath);

  final String displayName;
  final String modelPath;
}

/// Model specifications for each face detection model
class ModelSpecs {
  final int inputWidth;
  final int inputHeight;
  final int channels;
  final bool hasPostProcessing;
  final double defaultConfidenceThreshold;
  final String description;

  const ModelSpecs({
    required this.inputWidth,
    required this.inputHeight,
    this.channels = 3,
    this.hasPostProcessing = false,
    required this.defaultConfidenceThreshold,
    required this.description,
  });

  static const Map<FaceDetectionModel, ModelSpecs> specs = {
    FaceDetectionModel.blazeFace: ModelSpecs(
      inputWidth: 128,  // Correct YAML specification
      inputHeight: 224, // Correct YAML specification
      defaultConfidenceThreshold: 0.3, // Lowered for better detection
      description: 'Google BlazeFace - Optimized for mobile, dual output format',
    ),
    FaceDetectionModel.ultraFaceRFB: ModelSpecs(
      inputWidth: 320,  // Correct YAML specification
      inputHeight: 240, // Correct YAML specification
      defaultConfidenceThreshold: 0.4, // Lowered for better detection
      description: 'UltraFace RFB - Higher precision, anchor-based detection',
    ),
    FaceDetectionModel.ultraFaceSlim: ModelSpecs(
      inputWidth: 320,  // Same as RFB (both UltraFace variants)
      inputHeight: 240, // Same as RFB (both UltraFace variants)
      defaultConfidenceThreshold: 0.4, // Lowered for better detection
      description: 'UltraFace Slim - Faster inference, lightweight model',
    ),
    FaceDetectionModel.ultraFaceSlimINT8: ModelSpecs(
      inputWidth: 320,  // Same as FP32 version
      inputHeight: 240, // Same as FP32 version
      defaultConfidenceThreshold: 0.4, // Same as FP32 version
      description: 'UltraFace Slim INT8 - Quantized for maximum speed, 4x smaller model size',
    ),
    FaceDetectionModel.mediaPipe: ModelSpecs(
      inputWidth: 0, // Dynamic input size
      inputHeight: 0, // Dynamic input size
      defaultConfidenceThreshold: 0.5,
      description: 'Google MediaPipe - Hardware accelerated, ML Kit optimized',
    ),
  };

  static ModelSpecs getSpecs(FaceDetectionModel model) {
    return specs[model] ?? specs[FaceDetectionModel.blazeFace]!;
  }
}
