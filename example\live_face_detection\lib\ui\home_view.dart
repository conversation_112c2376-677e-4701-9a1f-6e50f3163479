import 'package:flutter/material.dart';
import 'package:live_face_detection/models/screen_params.dart';
import 'package:live_face_detection/ui/unified_camera_widget.dart';

/// [HomeView] displays [UnifiedCameraWidget] for multi-model face detection
class HomeView extends StatelessWidget {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    ScreenParams.screenSize = MediaQuery.sizeOf(context);
    return Scaffold(
      key: <PERSON>Key(),
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Row(
          children: [
            Image.asset(
              'assets/images/tfl_logo.png',
              fit: BoxFit.contain,
              height: 32,
            ),
            const SizedBox(width: 12),
            const Text(
              'Multi-Model Face Detection',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.face, color: Colors.white),
            onPressed: () {
              Navigator.pushNamed(context, '/mediapipe-demo');
            },
            tooltip: 'MediaPipe Demo',
          ),
        ],
      ),
      body: const UnifiedCameraWidget(),
    );
  }
}
