
BlazeFace:
  model_input:
    - name: normalized_input_image_tensor
      tensor: float32[1,224,128,3]
      identifier: 188
      quantization: linear
      q ≤ 255
  model_output:
    - name: raw_outputs/box_encodings
      identifier: 189
      dtype: float32
      shape: [1, 564, 16]
      description: |
        Tensor chứa các giá trị mã hoá (encoded boxes) đầu ra của detector.
        - Batch size: 1
        - Num_anchors: 564 (tương <PERSON>ng với số anchor boxes được gán)
        - Box encoding size: 16
          - C<PERSON> thể là: 4 box * 4 values (cho multiple boxes mỗi anchor), hoặc encoding đặc biệt.
        - Cần decode thông qua anchor box + scale để chuyển về [x1, y1, x2, y2] hoặc [cx, cy, w, h].

    - name: raw_outputs/class_predictions
      identifier: 190
      dtype: float32
      shape: [1, 564, 1]
      description: |
        Tensor chứa giá trị class score tương ứng với mỗi anchor box.
        - Batch size: 1
        - Num_anchors: 564
        - Num_classes: 1 (binary classification hoặc foreground/background)
        - <PERSON>p dụng sigmoid/softmax để lấy xác suất dự đoán.
        - <PERSON><PERSON><PERSON> hợp với box_encodings để chọn ra các object sau NMS (non-max suppression).


RFB_UltraFace:
  model_input:
    - name: input_1
      tensor: float32[-1,240,320,3]
      identifier: 0
  model_output:
    - name: boxes
      dtype: float32
      shape: [1, 4420, 4]
      description: |
        Tensor chứa toạ độ bounding box dự đoán cho mỗi anchor.
        - Batch size: 1
        - Num_anchors: 4420
        - 4 toạ độ mỗi box: [x1, y1, x2, y2] (hoặc có thể là [cx, cy, w, h] tùy vào decoder)
        - Toạ độ thường ở dạng normalized (0–1) theo width/height input image
        - Cần scale lại theo kích thước ảnh gốc nếu muốn vẽ lên ảnh

    - name: scores
      dtype: float32
      shape: [1, 4420, 2]
      description: |
        Tensor chứa xác suất classification (score) của mỗi anchor.
        - Batch size: 1
        - Num_anchors: 4420
        - 2 class scores: [background, face]
        - Thường dùng softmax/sigmoid để lấy xác suất
        - Lấy chỉ số class=1 (face) → score mặt người


Slim_UltraFace_INT8:
  model_input:
    - name: input_1
      tensor: uint8[-1,240,320,3]  # INT8 quantized input
      identifier: 0
      quantization:
        scale: 0.007843137718737125  # 1/127.5 for [0,255] -> [-1,1] normalization
        zero_point: 128  # Center point for INT8 quantization
  model_output:
    - name: boxes
      dtype: uint8  # INT8 quantized output
      shape: [1, 4420, 4]
      quantization:
        scale: 0.00390625  # Output dequantization scale
        zero_point: 0
      description: |
        Quantized tensor chứa toạ độ bounding box dự đoán cho mỗi anchor.
        - Batch size: 1
        - Num_anchors: 4420
        - 4 toạ độ mỗi box: [x1, y1, x2, y2] (quantized to INT8)
        - Cần dequantize: real_value = (quantized_value - zero_point) * scale
        - Toạ độ thường ở dạng normalized (0–1) theo width/height input image

    - name: scores
      dtype: uint8  # INT8 quantized output
      shape: [1, 4420, 2]
      quantization:
        scale: 0.00390625  # Output dequantization scale
        zero_point: 0
      description: |
        Quantized tensor chứa xác suất classification (score) của mỗi anchor.
        - Batch size: 1
        - Num_anchors: 4420
        - 2 class scores: [background, face] (quantized to INT8)
        - Cần dequantize: real_value = (quantized_value - zero_point) * scale
        - Thường dùng softmax/sigmoid để lấy xác suất
        - Lấy chỉ số class=1 (face) → score mặt người
