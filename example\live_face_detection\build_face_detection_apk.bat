@echo off
echo 🎯 Building Live Face Detection APK...

echo.
echo 📦 Getting Flutter dependencies...
flutter pub get

echo.
echo 🧹 Cleaning project...
flutter clean

echo.
echo 🔧 Building APK (Release)...
flutter build apk --release

echo.
echo 📊 Checking build results...
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo ✅ APK built successfully!
    for %%A in ("build\app\outputs\flutter-apk\app-release.apk") do (
        set /a size=%%~zA/1024/1024
        echo 📱 APK Size: !size!MB
    )
    echo 📍 Location: build\app\outputs\flutter-apk\app-release.apk
) else (
    echo ❌ APK build failed!
    echo Check the build output above for errors.
)

echo.
echo 📋 Next steps:
echo 1. Copy APK to Android device
echo 2. Enable "Unknown sources" in Settings
echo 3. Install APK and grant camera permissions
echo 4. Test face detection with good lighting

pause
