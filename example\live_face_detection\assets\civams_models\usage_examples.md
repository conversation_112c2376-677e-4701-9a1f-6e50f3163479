# 🚀 CIVAMS Models - Usage Examples

## 📋 Prerequisites
```bash
# Install TensorFlow Lite
pip install tensorflow
# or for mobile development
# Add TensorFlow Lite to your Android/iOS project
```

## 👤 Face Detection with BlazeFace

### Python Example
```python
import tensorflow as tf
import numpy as np
import cv2

# Load BlazeFace model
interpreter = tf.lite.Interpreter(model_path="bundled_models/blazeface.tfl")
interpreter.allocate_tensors()

# Get input and output details
input_details = interpreter.get_input_details()
output_details = interpreter.get_output_details()

def detect_faces(image):
    # Preprocess image
    input_shape = input_details[0]['shape']
    height, width = input_shape[1], input_shape[2]
    
    # Resize image
    resized = cv2.resize(image, (width, height))
    input_data = np.expand_dims(resized, axis=0).astype(np.float32)
    input_data = input_data / 255.0  # Normalize to [0, 1]
    
    # Run inference
    interpreter.set_tensor(input_details[0]['index'], input_data)
    interpreter.invoke()
    
    # Get results
    boxes = interpreter.get_tensor(output_details[0]['index'])
    scores = interpreter.get_tensor(output_details[1]['index'])
    
    return boxes, scores

# Usage
image = cv2.imread("your_image.jpg")
faces, confidences = detect_faces(image)
print(f"Detected {len(faces)} faces")
```

### Android/Kotlin Example
```kotlin
class BlazeFaceDetector {
    private lateinit var interpreter: Interpreter
    
    fun initialize(modelPath: String) {
        val model = loadModelFile(modelPath)
        interpreter = Interpreter(model)
    }
    
    fun detectFaces(bitmap: Bitmap): List<Face> {
        // Preprocess bitmap
        val inputArray = preprocessBitmap(bitmap)
        
        // Prepare output arrays
        val outputBoxes = Array(1) { Array(896) { FloatArray(4) } }
        val outputScores = Array(1) { FloatArray(896) }
        
        // Run inference
        interpreter.run(inputArray, mapOf(
            0 to outputBoxes,
            1 to outputScores
        ))
        
        // Process results
        return processResults(outputBoxes[0], outputScores[0])
    }
}
```

## 📍 Face Landmarks with Contours Model

### Python Example
```python
def extract_face_landmarks(face_image):
    # Load contours model
    interpreter = tf.lite.Interpreter(model_path="bundled_models/contours.tfl")
    interpreter.allocate_tensors()
    
    # Preprocess face image (typically 192x192)
    resized_face = cv2.resize(face_image, (192, 192))
    input_data = np.expand_dims(resized_face, axis=0).astype(np.float32) / 255.0
    
    # Run inference
    interpreter.set_tensor(interpreter.get_input_details()[0]['index'], input_data)
    interpreter.invoke()
    
    # Get landmarks (468 points)
    landmarks = interpreter.get_tensor(interpreter.get_output_details()[0]['index'])
    
    return landmarks.reshape(-1, 3)  # x, y, z coordinates

# Usage
face_region = image[y:y+h, x:x+w]  # Crop face from detection
landmarks = extract_face_landmarks(face_region)
print(f"Extracted {len(landmarks)} landmarks")
```

## 📊 Barcode Detection with MLKit Models

### Python Example
```python
def detect_barcodes(image):
    # Load barcode detection model
    interpreter = tf.lite.Interpreter(
        model_path="mlkit_models/barcode_ssd_mobilenet_v1_dmp25_quant.tflite"
    )
    interpreter.allocate_tensors()
    
    # Preprocess image
    input_details = interpreter.get_input_details()
    input_shape = input_details[0]['shape']
    
    resized = cv2.resize(image, (input_shape[2], input_shape[1]))
    input_data = np.expand_dims(resized, axis=0).astype(np.uint8)
    
    # Run inference
    interpreter.set_tensor(input_details[0]['index'], input_data)
    interpreter.invoke()
    
    # Get barcode regions
    output_details = interpreter.get_output_details()
    boxes = interpreter.get_tensor(output_details[0]['index'])
    
    return boxes

# Usage
barcodes = detect_barcodes(image)
print(f"Detected {len(barcodes)} barcodes")
```

## 🔧 Complete Pipeline Example

### Face Recognition Pipeline
```python
class CIVAMSFacePipeline:
    def __init__(self):
        self.face_detector = self.load_model("bundled_models/blazeface.tfl")
        self.landmark_detector = self.load_model("bundled_models/contours.tfl")
        self.eye_detector = self.load_model("bundled_models/BCLlefteyeclosed_200.emd")
    
    def process_image(self, image):
        # Step 1: Detect faces
        faces = self.detect_faces(image)
        
        results = []
        for face in faces:
            # Step 2: Extract landmarks
            landmarks = self.extract_landmarks(face)
            
            # Step 3: Check eye state (liveness)
            eye_state = self.check_eye_state(face)
            
            results.append({
                'face': face,
                'landmarks': landmarks,
                'eye_state': eye_state
            })
        
        return results
```

## 📱 Mobile Integration Tips

### Android TensorFlow Lite
```kotlin
// Add to build.gradle
implementation 'org.tensorflow:tensorflow-lite:2.13.0'
implementation 'org.tensorflow:tensorflow-lite-gpu:2.13.0'

// Load model from assets
private fun loadModelFile(modelPath: String): ByteBuffer {
    val fileDescriptor = assets.openFd(modelPath)
    val inputStream = FileInputStream(fileDescriptor.fileDescriptor)
    val fileChannel = inputStream.channel
    val startOffset = fileDescriptor.startOffset
    val declaredLength = fileDescriptor.declaredLength
    return fileChannel.map(FileChannel.MapMode.READ_ONLY, startOffset, declaredLength)
}
```

### iOS Core ML (if converting)
```swift
// Convert TFLite to Core ML first
import CoreML

class FaceDetector {
    private var model: MLModel?
    
    func loadModel() {
        guard let modelURL = Bundle.main.url(forResource: "blazeface", withExtension: "mlmodel") else {
            return
        }
        model = try? MLModel(contentsOf: modelURL)
    }
}
```

## ⚠️ Important Considerations
1. **Model Preprocessing**: Each model requires specific input preprocessing
2. **Performance**: Use GPU acceleration when available
3. **Memory**: Load models efficiently to avoid memory issues
4. **Threading**: Run inference on background threads
5. **Error Handling**: Always handle model loading and inference errors

## 🔗 Additional Resources
- [TensorFlow Lite Guide](https://www.tensorflow.org/lite)
- [MediaPipe Documentation](https://mediapipe.dev/)
- [OpenCV Documentation](https://opencv.org/)
