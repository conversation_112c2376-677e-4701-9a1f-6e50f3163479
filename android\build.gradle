// The Android Gradle Plugin builds the native code with the Android NDK.

group 'org.tensorflow.tflite_flutter'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        // The Android Gradle Plugin knows how to build native code with the NDK.
        classpath 'com.android.tools.build:gradle:7.3.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    // Bumping the plugin compileSdkVersion requires all clients of this plugin
    // to bump the version in their app.
    compileSdkVersion 31
    namespace 'org.tensorflow.tflite_flutter'

    // Bumping the plugin ndkVersion requires all clients of this plugin to bump
    // the version in their app and to download a newer version of the NDK.
    // ndkVersion "23.1.7779620"

    // Invoke the shared CMake build with the Android Gradle Plugin.
    externalNativeBuild {
        // cmake {
        //     path "../src/CMakeLists.txt"

        //     // The default CMake version for the Android Gradle Plugin is 3.10.2.
        //     // https://developer.android.com/studio/projects/install-ndk#vanilla_cmake
        //     //
        //     // The Flutter tooling requires that developers have CMake 3.10 or later
        //     // installed. You should not increase this version, as doing so will cause
        //     // the plugin to fail to compile for some customers of the plugin.
        //     // version "3.10.2"
        // }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion 19
    }
}


dependencies {
    def tflite_version = "2.11.0"
    
    implementation("org.tensorflow:tensorflow-lite:${tflite_version}")
    implementation("org.tensorflow:tensorflow-lite-gpu:${tflite_version}")
}
