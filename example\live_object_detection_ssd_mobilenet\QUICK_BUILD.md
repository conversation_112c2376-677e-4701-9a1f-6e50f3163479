# Hướng dẫn Build APK nhanh - Live Object Detection

## ✅ Đã hoàn thiện nâng cấp Gradle và Dependencies!

### Build Tools (Plugin DSL):
- Gradle version: 7.5 → 8.10.2 (latest stable)
- Android Gradle Plugin: 7.3.0 → 8.7.2 (k<PERSON><PERSON><PERSON> phụ<PERSON> bug Java 21)
- Java version: 1.8 → 17
- Kotlin version: 1.7.10 → 2.0.21 (latest stable)
- NDK version: flutter.ndkVersion → 27.0.12077973
- compileSdk & targetSdk: 35 (Android 15)

### Dependencies:
- tflite_flutter: path dependency → 0.11.0 (pub.dev)
- camera: 0.10.5+2 → 0.11.0+2 (latest stable)
- image: 4.0.17 → 4.2.0 (latest stable)
- path_provider: 2.0.15 → 2.1.5 (latest stable)
- image_picker: 1.0.0 → 1.1.2 (latest stable)
- exif: 3.1.4 → 3.3.0 (latest stable)

### Architecture:
- <PERSON><PERSON><PERSON><PERSON> từ imperative apply sang Plugin DSL syntax
- Cập nhật settings.gradle với pluginManagement và plugins blocks
- Xóa buildscript block cũ và sử dụng plugins block mới

## Cách 1: Sử dụng Command Prompt (Đơn giản nhất)

1. **Mở Command Prompt** (cmd) với quyền Administrator
2. **Copy và paste từng lệnh sau:**

```cmd
cd /d "c:\Users\<USER>\workspace\flutter-tflite\example\live_object_detection_ssd_mobilenet"
```

```cmd
flutter clean
flutter pub cache clean
```

```cmd
flutter pub get
```

```cmd
flutter build apk --release
```

3. **Tìm file APK tại:** `build\app\outputs\flutter-apk\app-release.apk`

## Cách 2: Sử dụng PowerShell

1. **Mở PowerShell** với quyền Administrator
2. **Chạy lệnh:**

```powershell
Set-Location "c:\Users\<USER>\workspace\flutter-tflite\example\live_object_detection_ssd_mobilenet"
flutter clean
flutter pub get
flutter build apk --release
```

## Cách 3: Sử dụng Script tự động

1. **Chạy batch script:**
```cmd
build_apk.bat
```

2. **Hoặc PowerShell script:**
```powershell
.\build_apk.ps1
```

## Kiểm tra kết quả

Sau khi build thành công, bạn sẽ thấy:
- File APK tại: `build\app\outputs\flutter-apk\app-release.apk`
- Kích thước file: 76.8MB
- Thông báo "Built build\app\outputs\flutter-apk\app-release.apk"

## Xử lý lỗi thường gặp

### Lỗi "Flutter not found"
```cmd
flutter doctor
```
Nếu lỗi, cài đặt lại Flutter hoặc thêm vào PATH

### Lỗi dependencies
```cmd
flutter clean
flutter pub get
```

### Lỗi Android SDK
```cmd
flutter doctor --android-licenses
```

### Lỗi symlink (Windows)
```cmd
start ms-settings:developers
```
Bật Developer Mode trong Windows Settings

## Cài đặt APK

1. Copy file APK vào điện thoại Android
2. Bật "Unknown sources" trong Settings
3. Tap vào file APK để cài đặt
4. Cấp quyền Camera và Storage
5. Mở app và test với camera real-time

## Tính năng của App

- **Live Object Detection**: Nhận diện đối tượng real-time qua camera
- **SSD MobileNet**: Model AI hiệu quả cho mobile
- **Camera Integration**: Tích hợp camera với xử lý frame
- **Real-time Inference**: Xử lý TensorFlow Lite real-time
- **Visual Feedback**: Hiển thị bounding boxes và confidence scores

## So sánh với Static Detection

| Tính năng | Live Detection | Static Detection |
|-----------|----------------|------------------|
| **Input** | Camera feed | Ảnh/Gallery |
| **Xử lý** | Real-time | Theo yêu cầu |
| **Kích thước** | 76.8MB | 83.6MB |
| **Sử dụng** | Quét live | Phân tích ảnh |
