import 'dart:async';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:live_face_detection/models/recognition.dart';
import 'package:live_face_detection/service/simple_face_detector.dart';

/// Simple Camera Widget for Face Detection
/// Focuses on core functionality: camera streaming + face detection
class SimpleCameraWidget extends StatefulWidget {
  const SimpleCameraWidget({super.key});

  @override
  State<SimpleCameraWidget> createState() => _SimpleCameraWidgetState();
}

class _SimpleCameraWidgetState extends State<SimpleCameraWidget>
    with WidgetsBindingObserver {
  
  // Camera
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;
  
  // Face Detection
  SimpleFaceDetector? _faceDetector;
  List<Recognition> _detections = [];
  
  // State
  bool _isInitialized = false;
  bool _isProcessing = false;
  String _status = 'Initializing...';
  
  // Stats
  Map<String, int> _stats = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeApp();
  }

  /// Initialize camera and face detector
  Future<void> _initializeApp() async {
    setState(() {
      _status = 'Loading face detection model...';
    });
    
    // Initialize face detector first
    await _initializeFaceDetector();
    
    setState(() {
      _status = 'Initializing camera...';
    });
    
    // Initialize camera
    await _initializeCamera();
    
    setState(() {
      _status = 'Ready';
      _isInitialized = true;
    });
  }

  /// Initialize face detector
  Future<void> _initializeFaceDetector() async {
    try {
      _faceDetector = SimpleFaceDetector();
      final success = await _faceDetector!.initialize();
      
      if (!success) {
        setState(() {
          _status = 'Failed to load face detection model';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
      print('Error initializing face detector: $e');
    }
  }

  /// Initialize camera
  Future<void> _initializeCamera() async {
    try {
      _cameras = await availableCameras();
      
      if (_cameras == null || _cameras!.isEmpty) {
        setState(() {
          _status = 'No cameras available';
        });
        return;
      }

      // Use front camera if available, otherwise back camera
      CameraDescription camera = _cameras!.first;
      for (final cam in _cameras!) {
        if (cam.lensDirection == CameraLensDirection.back) {
          camera = cam;
          break;
        }
      }

      _cameraController = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
      );

      await _cameraController!.initialize();
      
      // Start image stream for face detection
      await _cameraController!.startImageStream(_processCameraImage);
      
    } catch (e) {
      setState(() {
        _status = 'Camera error: $e';
      });
      print('Error initializing camera: $e');
    }
  }

  /// Process camera image for face detection
  void _processCameraImage(CameraImage image) async {
    if (_isProcessing || _faceDetector == null || !_faceDetector!.isReady) {
      return;
    }

    _isProcessing = true;

    try {
      // Detect faces
      final detections = await _faceDetector!.detectFaces(image);
      
      // Get stats
      final stats = _faceDetector!.getStats();

      // Update UI
      if (mounted) {
        setState(() {
          _detections = detections;
          _stats = stats;
        });
      }
    } catch (e) {
      print('Error processing camera image: $e');
    } finally {
      _isProcessing = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized || _cameraController == null) {
      return _buildLoadingScreen();
    }

    if (!_cameraController!.value.isInitialized) {
      return _buildLoadingScreen();
    }

    return Stack(
      children: [
        // Camera preview
        Positioned.fill(
          child: CameraPreview(_cameraController!),
        ),
        
        // Face detection overlays
        ..._buildFaceOverlays(),
        
        // Status and stats
        _buildStatusOverlay(),
      ],
    );
  }

  /// Build loading screen
  Widget _buildLoadingScreen() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              color: Colors.white,
            ),
            const SizedBox(height: 16),
            Text(
              _status,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build face detection overlays
  List<Widget> _buildFaceOverlays() {
    if (_detections.isEmpty) return [];

    return _detections.map((detection) {
      return Positioned(
        left: detection.location.left,
        top: detection.location.top,
        width: detection.location.width,
        height: detection.location.height,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.green,
              width: 2,
            ),
          ),
          child: Align(
            alignment: Alignment.topLeft,
            child: Container(
              color: Colors.green,
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              child: Text(
                '${detection.label} ${(detection.score * 100).toInt()}%',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      );
    }).toList();
  }

  /// Build status overlay
  Widget _buildStatusOverlay() {
    return Positioned(
      top: 50,
      left: 16,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Faces: ${_detections.length}',
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
            if (_stats.isNotEmpty) ...[
              Text(
                'Inference: ${_stats['inferenceTime']}ms',
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
              Text(
                'Total: ${_stats['totalTime']}ms',
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ],
          ],
        ),
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      _cameraController?.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _initializeCamera();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cameraController?.dispose();
    _faceDetector?.dispose();
    super.dispose();
  }
}
