@echo off
echo 🚀 Copying CIVAMS Models for Live Face Detection...

echo.
echo 📁 Creating assets directory structure...
if not exist "assets" mkdir assets
if not exist "assets\civams_models" mkdir assets\civams_models

echo.
echo 📋 Copying models from civams_models to assets...

echo Copying BlazeFace model...
copy "assets\civams_models\bundled_models\blazeface.tfl" "assets\civams_models\" 2>nul
if exist "assets\civams_models\blazeface.tfl" (
    echo ✅ BlazeFace model copied successfully
) else (
    echo ❌ BlazeFace model not found or copy failed
)

echo Copying Contours model...
copy "assets\civams_models\bundled_models\contours.tfl" "assets\civams_models\" 2>nul
if exist "assets\civams_models\contours.tfl" (
    echo ✅ Contours model copied successfully
) else (
    echo ❌ Contours model not found or copy failed
)

echo.
echo 📊 Checking model files...
if exist "assets\civams_models\blazeface.tfl" (
    for %%A in ("assets\civams_models\blazeface.tfl") do echo BlazeFace: %%~zA bytes
)
if exist "assets\civams_models\contours.tfl" (
    for %%A in ("assets\civams_models\contours.tfl") do echo Contours: %%~zA bytes
)

echo.
echo ✅ Model copy completed!
echo.
echo 📋 Next steps:
echo 1. Run flutter pub get
echo 2. Run flutter build apk --release
echo 3. Test face detection on device

pause
