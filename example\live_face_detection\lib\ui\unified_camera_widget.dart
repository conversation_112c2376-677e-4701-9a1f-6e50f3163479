// Unified Camera Widget with Model Selection
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:live_face_detection/models/recognition.dart';
import 'package:live_face_detection/models/face_detection_model.dart';
import 'package:live_face_detection/service/unified_face_detector.dart';
import 'package:live_face_detection/ui/model_selection_widget.dart';
import 'package:live_face_detection/service/detailed_performance_tracker.dart';
import 'package:live_face_detection/service/lightweight_performance_tracker.dart';
import 'package:live_face_detection/service/detector_comparison_service.dart';
import 'package:live_face_detection/ui/enhanced_performance_display.dart';
import 'package:live_face_detection/service/device_calibration_service.dart';
import 'package:live_face_detection/ui/calibration_widget.dart';

/// Unified Camera Widget with multiple model support
class UnifiedCameraWidget extends StatefulWidget {
  const UnifiedCameraWidget({Key? key}) : super(key: key);

  @override
  State<UnifiedCameraWidget> createState() => _UnifiedCameraWidgetState();
}

class _UnifiedCameraWidgetState extends State<UnifiedCameraWidget> {
  CameraController? _cameraController;
  UnifiedFaceDetector? _faceDetector;
  DetectorComparisonService? _detectorComparison;
  
  bool _isInitialized = false;
  bool _isDetecting = false;
  bool _showModelSelection = false;
  bool _showCalibration = false;
  bool _flipHorizontally = false; // Toggle for Telpo F8 horizontal flip
  bool _flipVertically = false; // Toggle for Telpo F8 vertical flip

  List<Recognition> _recognitions = [];
  Size _imageSize = Size.zero;
  
  // Performance metrics
  int _frameCount = 0;
  DateTime _lastFpsUpdate = DateTime.now();
  double _fps = 0.0;

  // Frame skipping for performance (optimized for smooth operation)
  int _frameSkipCount = 0;
  static const int _frameSkipInterval = 0; // DEBUG: Disable frame skipping to test BlazeFace

  @override
  void initState() {
    super.initState();
    _initializeCamera();
    _initializeCalibration();
  }

  /// Initialize device calibration service
  Future<void> _initializeCalibration() async {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await DeviceCalibrationService.instance.initialize(context);
    });
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    _faceDetector?.dispose();
    super.dispose();
  }

  Future<void> _initializeCamera() async {
    try {
      // Get available cameras
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        print('❌ No cameras available');
        return;
      }

      // Find back camera (or front if back not available)
      CameraDescription? selectedCamera;
      for (final cam in cameras) {
        if (cam.lensDirection == CameraLensDirection.back) {
          selectedCamera = cam;
          break;
        }
      }
      selectedCamera ??= cameras.first;

      // Initialize camera controller
      _cameraController = CameraController(
        selectedCamera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.yuv420,
      );

      await _cameraController!.initialize();

      // Initialize face detector
      _faceDetector = UnifiedFaceDetector();
      final success = await _faceDetector!.initializeAllModels();

      if (!success) {
        print('❌ Failed to initialize face detection models');
        return;
      }

      // Debug: Check initial model
      print('🔍 DEBUG: Initial model after initialization: ${_faceDetector!.currentModel.displayName}');

      // DEBUG: Force BlazeFace for debugging
      _faceDetector!.forceBlazeFace();

      print('🔍 DEBUG: UI will use model: ${_faceDetector!.currentModel.displayName}');

      // Start image stream
      _cameraController!.startImageStream(_processCameraImage);

      setState(() {
        _isInitialized = true;
      });

      // Start periodic performance reporting
      DetailedPerformanceTracker.startPeriodicReporting();

      print('✅ Camera and face detection initialized successfully');
    } catch (e) {
      print('❌ Error initializing camera: $e');
    }
  }

  Future<void> _processCameraImage(CameraImage image) async {
    if (_isDetecting || _faceDetector == null) return;

    // Aggressive frame skipping for better performance
    _frameSkipCount++;
    if (_frameSkipCount < _frameSkipInterval) {
      return; // Skip frame
    }
    _frameSkipCount = 0; // Reset and process this frame

    _isDetecting = true;

    try {
      // Update image size
      _imageSize = Size(image.width.toDouble(), image.height.toDouble());

      // Detect faces without heavy performance tracking
      final recognitions = await _faceDetector!.detectFaces(image);

      // Update FPS
      _updateFPS();

      setState(() {
        _recognitions = recognitions;
      });
    } catch (e) {
      print('Error processing camera image: $e');
    } finally {
      _isDetecting = false;
    }
  }

  void _updateFPS() {
    _frameCount++;
    final now = DateTime.now();
    final diff = now.difference(_lastFpsUpdate).inMilliseconds;

    if (diff >= 1000) {
      _fps = _frameCount * 1000 / diff;
      _frameCount = 0;
      _lastFpsUpdate = now;
    }
  }

  String _getInferenceTime() {
    if (_faceDetector == null) return '0';
    final metrics = _faceDetector!.getCurrentModelMetrics();
    return metrics['inferenceTime']?.toString() ?? '0';
  }

  String _getInputSize() {
    if (_faceDetector == null) return '0x0';
    final specs = _faceDetector!.getCurrentModelSpecs();
    return '${specs.inputWidth}x${specs.inputHeight}';
  }

  void _onModelChanged(FaceDetectionModel newModel) {
    setState(() {
      _recognitions.clear(); // Clear previous detections
    });
    print('🔄 UI Model switched to ${newModel.displayName}');
    print('🔍 Current detector model: ${_faceDetector?.currentModel.displayName}');
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized || _cameraController == null) {
      return const Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Colors.blue),
              SizedBox(height: 16),
              Text(
                'Initializing Camera & Models...',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera Preview with Overlay
          Positioned.fill(
            child: AspectRatio(
              aspectRatio: _cameraController!.value.aspectRatio,
              child: Stack(
                children: [
                  // Camera Preview
                  CameraPreview(_cameraController!),

                  // Face Detection Overlay (matches camera preview area)
                  Positioned.fill(
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        return CustomPaint(
                          painter: FaceDetectionPainter(
                            recognitions: _recognitions,
                            imageSize: _imageSize,
                            previewSize: Size(constraints.maxWidth, constraints.maxHeight),
                            cameraController: _cameraController!,
                            flipHorizontally: _flipHorizontally,
                            flipVertically: _flipVertically,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Top Controls
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            right: 16,
            child: Row(
              children: [
                // Model Selection Toggle
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: Icon(
                      _showModelSelection ? Icons.close : Icons.psychology,
                      color: Colors.blue,
                    ),
                    onPressed: () {
                      setState(() {
                        _showModelSelection = !_showModelSelection;
                        _showCalibration = false; // Close calibration when opening model selection
                      });
                    },
                    tooltip: _showModelSelection ? 'Close' : 'Model Selection',
                  ),
                ),

                const SizedBox(width: 8),

                // Calibration Toggle
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: Icon(
                      _showCalibration ? Icons.close : Icons.tune,
                      color: Colors.orange,
                    ),
                    onPressed: () {
                      setState(() {
                        _showCalibration = !_showCalibration;
                        _showModelSelection = false; // Close model selection when opening calibration
                      });
                    },
                    tooltip: _showCalibration ? 'Close' : 'Calibration',
                  ),
                ),

                const SizedBox(height: 8),

                // Horizontal Flip Toggle for Telpo F8
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: Icon(
                      _flipHorizontally ? Icons.flip : Icons.flip_outlined,
                      color: _flipHorizontally ? Colors.green : Colors.white,
                    ),
                    onPressed: () {
                      setState(() {
                        _flipHorizontally = !_flipHorizontally;
                      });
                    },
                    tooltip: 'Toggle horizontal flip for Telpo F8',
                  ),
                ),

                // Vertical Flip Toggle for Telpo F8
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: Icon(
                      _flipVertically ? Icons.swap_vert : Icons.swap_vert_outlined,
                      color: _flipVertically ? Colors.green : Colors.white,
                    ),
                    onPressed: () {
                      setState(() {
                        _flipVertically = !_flipVertically;
                      });
                    },
                    tooltip: 'Toggle vertical flip for Telpo F8',
                  ),
                ),

                const Spacer(),
                
                // Performance Info
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${_faceDetector?.currentModel.displayName ?? 'Unknown'}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'FPS: ${_fps.toStringAsFixed(1)}',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 10,
                        ),
                      ),
                      Text(
                        'Faces: ${_recognitions.length}',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 10,
                        ),
                      ),
                      if (_faceDetector != null) ...[
                        Text(
                          'Inference: ${_getInferenceTime()}ms',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 10,
                          ),
                        ),
                        Text(
                          'Input: ${_getInputSize()}',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Model Selection Panel
          if (_showModelSelection && _faceDetector != null)
            Positioned(
              top: MediaQuery.of(context).padding.top + 80,
              left: 16,
              right: 16,
              child: ModelSelectionWidget(
                detector: _faceDetector!,
                onModelChanged: _onModelChanged,
              ),
            ),

          // Calibration Panel
          if (_showCalibration)
            Positioned(
              top: MediaQuery.of(context).padding.top + 80,
              left: 16,
              right: 16,
              child: CalibrationWidget(
                onCalibrationChanged: () {
                  setState(() {
                    // Trigger repaint to apply new calibration
                  });
                },
              ),
            ),

          // Bottom Info
          Positioned(
            bottom: MediaQuery.of(context).padding.bottom + 16,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.face, color: Colors.green, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Live Face Detection',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (_faceDetector != null) ...[
                    Text(
                      _faceDetector!.getCurrentModelSpecs().description,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 10,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Custom painter for drawing face detection results
class FaceDetectionPainter extends CustomPainter {
  final List<Recognition> recognitions;
  final Size imageSize;
  final Size previewSize;
  final CameraController cameraController;
  final bool flipHorizontally;
  final bool flipVertically;

  FaceDetectionPainter({
    required this.recognitions,
    required this.imageSize,
    required this.previewSize,
    required this.cameraController,
    required this.flipHorizontally,
    required this.flipVertically,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (recognitions.isEmpty || imageSize == Size.zero) return;

    final paint = Paint()
      ..color = Colors.green
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    final debugPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Debug: Draw image bounds
    final imageBounds = Rect.fromLTWH(0, 0, imageSize.width, imageSize.height);
    final scaledImageBounds = _scaleRect(imageBounds);
    canvas.drawRect(scaledImageBounds, debugPaint);

    for (final recognition in recognitions) {
      final originalRect = recognition.location;
      final scaledRect = _scaleRect(originalRect);

      // Draw bounding box
      canvas.drawRect(scaledRect, paint);

      // Draw confidence score and debug info
      final textSpan = TextSpan(
        text: '${(recognition.score * 100).toStringAsFixed(1)}%\n'
              'Orig: ${originalRect.left.toInt()},${originalRect.top.toInt()}\n'
              'Scaled: ${scaledRect.left.toInt()},${scaledRect.top.toInt()}',
        style: const TextStyle(
          color: Colors.green,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(scaledRect.left, scaledRect.top - 60),
      );
    }

    // Debug: Draw coordinate system info
    final debugTextSpan = TextSpan(
      text: 'Image: ${imageSize.width.toInt()}x${imageSize.height.toInt()}\n'
            'Preview: ${previewSize.width.toInt()}x${previewSize.height.toInt()}\n'
            'Camera AR: ${cameraController.value.aspectRatio.toStringAsFixed(2)}',
      style: const TextStyle(
        color: Colors.yellow,
        fontSize: 12,
        fontWeight: FontWeight.bold,
      ),
    );

    final debugTextPainter = TextPainter(
      text: debugTextSpan,
      textDirection: TextDirection.ltr,
    );

    debugTextPainter.layout();
    debugTextPainter.paint(canvas, const Offset(10, 10));
  }

  Rect _scaleRect(Rect rect) {
    if (imageSize == Size.zero || previewSize == Size.zero) {
      return rect;
    }

    // Since the camera preview is wrapped in AspectRatio widget,
    // the previewSize already represents the actual camera preview area.
    // We just need to scale from image coordinates to preview coordinates.

    // Scale coordinates from image space to preview space
    final scaleX = previewSize.width / imageSize.width;
    final scaleY = previewSize.height / imageSize.height;

    // Check if we need to flip coordinates for Telpo F8 or front-facing cameras
    final needsHorizontalFlip = _shouldFlipHorizontally();
    final needsVerticalFlip = _shouldFlipVertically();

    double left, right;
    if (needsHorizontalFlip) {
      // Flip horizontally for mirrored cameras (Telpo F8)
      left = (imageSize.width - rect.right) * scaleX;
      right = (imageSize.width - rect.left) * scaleX;
    } else {
      left = rect.left * scaleX;
      right = rect.right * scaleX;
    }

    double top, bottom;
    if (needsVerticalFlip) {
      // Flip vertically for Telpo F8 (both cameras on front side)
      top = (imageSize.height - rect.bottom) * scaleY;
      bottom = (imageSize.height - rect.top) * scaleY;
    } else {
      top = rect.top * scaleY;
      bottom = rect.bottom * scaleY;
    }

    // Apply scaling (no offset needed since AspectRatio widget handles positioning)
    return Rect.fromLTRB(
      left,
      top,
      right,
      bottom,
    );
  }

  /// Check if we should flip coordinates horizontally
  /// Returns true for Telpo F8 or front-facing cameras that show mirrored image
  bool _shouldFlipHorizontally() {
    // Use manual toggle for Telpo F8 or other devices that need flipping
    if (flipHorizontally) {
      return true;
    }

    // Auto-detect front-facing cameras (usually mirrored)
    final cameraDescription = cameraController.description;
    if (cameraDescription.lensDirection == CameraLensDirection.front) {
      return true;
    }

    return false; // Default: no flip for normal back cameras
  }

  /// Check if we should flip coordinates vertically
  /// Returns true for Telpo F8 where both cameras are on front side
  bool _shouldFlipVertically() {
    // Use manual toggle for Telpo F8 or other devices that need vertical flipping
    if (flipVertically) {
      return true;
    }

    // For Telpo F8, you might want to auto-detect based on device model
    // Add device-specific detection here if needed

    return false; // Default: no vertical flip for normal cameras
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
