// UltraFace Detection Service
import 'dart:math' as math;
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:tflite_flutter/tflite_flutter.dart';
import 'package:live_face_detection/models/recognition.dart';
import 'package:live_face_detection/models/face_detection_model.dart';
import 'package:live_face_detection/utils/image_utils.dart';
import 'package:live_face_detection/service/detailed_performance_tracker.dart';

/// UltraFace Detection Service
/// Supports both RFB and Slim variants
class UltraFaceDetector {
  Interpreter? _interpreter;
  bool _isInitialized = false;
  bool _isProcessing = false;
  
  // Model configuration
  late FaceDetectionModel _modelType;
  late ModelSpecs _modelSpecs;
  late double _confidenceThreshold;

  // Quantization support
  bool _isQuantized = false;
  
  // Performance metrics
  int _inferenceTime = 0;
  int _totalTime = 0;
  
  // UltraFace anchor configuration
  late List<List<int>> _featureMaps;
  late List<List<int>> _minBoxes;
  late List<List<double>> _anchorsXY;
  late List<List<double>> _anchorsWH;
  
  // Variance parameters (optimized)
  static const double _centerVariance = 0.1;
  static const double _sizeVariance = 0.2;
  static const double _nmsIouThreshold = 0.4; // Relaxed for speed
  static const int _nmsMaxOutputSize = 50; // Reduced for speed

  /// Initialize UltraFace detector
  Future<bool> initialize(FaceDetectionModel modelType) async {
    try {
      print('🔄 Loading ${modelType.displayName} model...');
      
      _modelType = modelType;
      _modelSpecs = ModelSpecs.getSpecs(modelType);
      _confidenceThreshold = _modelSpecs.defaultConfidenceThreshold;

      // Check if this is a quantized model
      _isQuantized = modelType == FaceDetectionModel.ultraFaceSlimINT8;
      
      // Initialize anchor configuration
      _initializeAnchors();
      
      // Load TensorFlow Lite model with optimizations
      final options = InterpreterOptions();
      options.threads = 2; // Reduced threads for better performance and stability
      _interpreter = await Interpreter.fromAsset(_modelType.modelPath, options: options);
      
      // Print model information
      _printModelInfo();
      
      _isInitialized = true;
      print('✅ ${modelType.displayName} detector initialized successfully');
      return true;
    } catch (e) {
      print('❌ Error initializing ${modelType.displayName} detector: $e');
      return false;
    }
  }
  
  /// Initialize anchor configuration for UltraFace
  void _initializeAnchors() {
    // UltraFace anchor configuration
    _featureMaps = [[40, 30], [20, 15], [10, 8], [5, 4]];
    _minBoxes = [[10, 16, 24], [32, 48], [64, 96], [128, 192, 256]];
    
    // Generate anchors
    final anchors = _generateAnchors();
    _anchorsXY = anchors['xy']!;
    _anchorsWH = anchors['wh']!;
  }
  
  /// Generate anchor points for UltraFace
  Map<String, List<List<double>>> _generateAnchors() {
    final anchorsXY = <List<double>>[];
    final anchorsWH = <List<double>>[];
    
    for (int i = 0; i < _featureMaps.length; i++) {
      final featureMap = _featureMaps[i];
      final minBox = _minBoxes[i];
      
      for (int y = 0; y < featureMap[1]; y++) {
        for (int x = 0; x < featureMap[0]; x++) {
          for (int minSize in minBox) {
            // Anchor center coordinates (normalized)
            final centerX = (x + 0.5) / featureMap[0];
            final centerY = (y + 0.5) / featureMap[1];
            
            // Anchor size (normalized)
            final sizeX = minSize / _modelSpecs.inputWidth;
            final sizeY = minSize / _modelSpecs.inputHeight;
            
            anchorsXY.add([centerX, centerY]);
            anchorsWH.add([sizeX, sizeY]);
          }
        }
      }
    }
    
    return {'xy': anchorsXY, 'wh': anchorsWH};
  }

  /// Print model information
  void _printModelInfo() {
    if (_interpreter == null) return;
    
    print('\n=== ${_modelType.displayName.toUpperCase()} MODEL INFO ===');
    print('Input tensors: ${_interpreter!.getInputTensors().length}');
    print('Output tensors: ${_interpreter!.getOutputTensors().length}');
    
    for (int i = 0; i < _interpreter!.getInputTensors().length; i++) {
      final tensor = _interpreter!.getInputTensor(i);
      print('Input $i: ${tensor.shape} (${tensor.type})');
    }
    
    for (int i = 0; i < _interpreter!.getOutputTensors().length; i++) {
      final tensor = _interpreter!.getOutputTensor(i);
      print('Output $i: ${tensor.shape} (${tensor.type})');
    }
    print('Anchors generated: ${_anchorsXY.length}');
  }

  /// Detect faces in camera image
  Future<List<Recognition>> detectFaces(CameraImage cameraImage) async {
    if (!_isInitialized || _interpreter == null || _isProcessing) {
      return [];
    }
    
    _isProcessing = true;
    final stopwatch = Stopwatch()..start();
    
    try {
      // Convert camera image to RGB
      final rgbImage = await _convertCameraImageToRGB(cameraImage);
      if (rgbImage == null) return [];
      
      // Resize to model input size
      final resized = img.copyResize(
        rgbImage, 
        width: _modelSpecs.inputWidth, 
        height: _modelSpecs.inputHeight
      );
      
      // Convert to input tensor (quantized or float)
      final input = _isQuantized ? _imageToQuantizedInputTensor(resized) : _imageToInputTensor(resized);
      
      // Run inference
      final detections = await _runInference(input, cameraImage.width, cameraImage.height);
      
      stopwatch.stop();
      _totalTime = stopwatch.elapsedMilliseconds;
      
      return detections;
    } catch (e) {
      print('Error detecting faces: $e');
      return [];
    } finally {
      _isProcessing = false;
    }
  }
  
  /// Convert camera image to RGB
  Future<img.Image?> _convertCameraImageToRGB(CameraImage cameraImage) async {
    try {
      // Add null checks for camera image planes
      if (cameraImage.planes.isEmpty || 
          cameraImage.planes.length < 3 ||
          cameraImage.planes[0].bytes.isEmpty) {
        print('Invalid camera image format');
        return null;
      }
      
      return ImageUtils.convertYUV420ToImage(cameraImage);
    } catch (e) {
      print('Error converting camera image: $e');
      return null;
    }
  }

  /// Convert image to input tensor (FP32)
  List<List<List<List<double>>>> _imageToInputTensor(img.Image image) {
    final input = List.generate(1, (_) =>
      List.generate(_modelSpecs.inputHeight, (y) =>
        List.generate(_modelSpecs.inputWidth, (x) =>
          List.generate(3, (c) {
            final pixel = image.getPixel(x, y);
            switch (c) {
              case 0: return pixel.r / 255.0; // Red
              case 1: return pixel.g / 255.0; // Green
              case 2: return pixel.b / 255.0; // Blue
              default: return 0.0;
            }
          })
        )
      )
    );
    return input;
  }

  /// Convert image to quantized input tensor (INT8)
  List<List<List<List<int>>>> _imageToQuantizedInputTensor(img.Image image) {
    final input = List.generate(1, (_) =>
      List.generate(_modelSpecs.inputHeight, (y) =>
        List.generate(_modelSpecs.inputWidth, (x) =>
          List.generate(3, (c) {
            final pixel = image.getPixel(x, y);
            int value;
            switch (c) {
              case 0: value = pixel.r.toInt(); // Red [0-255]
              case 1: value = pixel.g.toInt(); // Green [0-255]
              case 2: value = pixel.b.toInt(); // Blue [0-255]
              default: value = 0;
            }
            return value;
          })
        )
      )
    );
    return input;
  }

  /// Run inference on input tensor
  Future<List<Recognition>> _runInference(
    dynamic input, // Can be List<List<List<List<double>>>> or List<List<List<List<int>>>>
    int originalWidth,
    int originalHeight,
  ) async {
    final inferenceStopwatch = Stopwatch()..start();
    
    try {
      // Check if interpreter is still available
      if (_interpreter == null) {
        print('Interpreter is null during inference');
        return [];
      }
      
      // Prepare output tensors for UltraFace
      // UltraFace outputs: boxes and scores
      dynamic outputBoxes, outputScores;

      if (_isQuantized) {
        // INT8 quantized outputs
        outputBoxes = [List.generate(_anchorsXY.length, (_) => List.filled(4, 0))];
        outputScores = [List.generate(_anchorsXY.length, (_) => List.filled(2, 0))];
      } else {
        // FP32 outputs
        outputBoxes = [List.generate(_anchorsXY.length, (_) => List.filled(4, 0.0))];
        outputScores = [List.generate(_anchorsXY.length, (_) => List.filled(2, 0.0))];
      }

      final outputs = <int, Object>{
        0: outputBoxes,
        1: outputScores,
      };
      
      // Run inference
      _interpreter!.runForMultipleInputs([input], outputs);
      
      inferenceStopwatch.stop();
      _inferenceTime = inferenceStopwatch.elapsedMilliseconds;
      
      // Process results (handle quantized vs float outputs)
      if (_isQuantized) {
        return _processQuantizedUltraFaceResults(
          outputBoxes[0],
          outputScores[0],
          originalWidth,
          originalHeight
        );
      } else {
        return _processUltraFaceResults(
          outputBoxes[0],
          outputScores[0],
          originalWidth,
          originalHeight
        );
      }
    } catch (e) {
      print('Error running inference: $e');
      return [];
    }
  }

  /// Process UltraFace inference results
  List<Recognition> _processUltraFaceResults(
    List<List<double>> boxes,
    List<List<double>> scores,
    int originalWidth,
    int originalHeight,
  ) {
    final results = <Recognition>[];
    
    // Decode boxes and filter by confidence
    final decodedBoxes = _decodeBoxes(boxes);
    
    for (int i = 0; i < decodedBoxes.length; i++) {
      final scoreList = scores[i];
      
      // Extract confidence score (class 1 = face)
      final score = scoreList.length > 1 ? scoreList[1] : 0.0;
      
      if (score > _confidenceThreshold) {
        final box = decodedBoxes[i];
        
        // Convert normalized coordinates to actual coordinates
        final x1 = (box[0] * originalWidth).clamp(0.0, originalWidth.toDouble());
        final y1 = (box[1] * originalHeight).clamp(0.0, originalHeight.toDouble());
        final x2 = (box[2] * originalWidth).clamp(0.0, originalWidth.toDouble());
        final y2 = (box[3] * originalHeight).clamp(0.0, originalHeight.toDouble());
        
        final rect = Rect.fromLTRB(x1, y1, x2, y2);
        
        results.add(Recognition(
          i,        // id
          'face',   // label
          score,    // score
          rect,     // location
        ));
      }
    }
    
    // Apply Non-Maximum Suppression
    final nmsResults = _applyNMS(results);
    
    // Sort by confidence and take top 10
    nmsResults.sort((a, b) => b.score.compareTo(a.score));
    return nmsResults.take(10).toList();
  }

  /// Process quantized UltraFace inference results (INT8)
  List<Recognition> _processQuantizedUltraFaceResults(
    List<List<int>> boxes,
    List<List<int>> scores,
    int originalWidth,
    int originalHeight,
  ) {
    final results = <Recognition>[];

    // Dequantize and decode boxes
    final dequantizedBoxes = _dequantizeBoxes(boxes);
    final decodedBoxes = _decodeBoxes(dequantizedBoxes);

    for (int i = 0; i < decodedBoxes.length; i++) {
      final scoreList = scores[i];

      // Dequantize and extract confidence score (class 1 = face)
      final quantizedScore = scoreList.length > 1 ? scoreList[1] : 0;
      final score = _dequantizeScore(quantizedScore);

      if (score > _confidenceThreshold) {
        final box = decodedBoxes[i];

        // Convert normalized coordinates to actual coordinates
        final x1 = (box[0] * originalWidth).clamp(0.0, originalWidth.toDouble());
        final y1 = (box[1] * originalHeight).clamp(0.0, originalHeight.toDouble());
        final x2 = (box[2] * originalWidth).clamp(0.0, originalWidth.toDouble());
        final y2 = (box[3] * originalHeight).clamp(0.0, originalHeight.toDouble());

        final rect = Rect.fromLTRB(x1, y1, x2, y2);

        results.add(Recognition(
          i,        // id
          'face',   // label
          score,    // score
          rect,     // location
        ));
      }
    }

    // Apply Non-Maximum Suppression
    final nmsResults = _applyNMS(results);

    // Sort by confidence and take top 10
    nmsResults.sort((a, b) => b.score.compareTo(a.score));
    return nmsResults.take(10).toList();
  }

  /// Dequantize INT8 boxes to float
  List<List<double>> _dequantizeBoxes(List<List<int>> quantizedBoxes) {
    const double scale = 0.00390625; // From model_output.yml
    const int zeroPoint = 0;

    return quantizedBoxes.map((box) =>
      box.map((value) => (value - zeroPoint) * scale).toList()
    ).toList();
  }

  /// Dequantize INT8 score to float
  double _dequantizeScore(int quantizedScore) {
    const double scale = 0.00390625; // From model_output.yml
    const int zeroPoint = 0;
    return (quantizedScore - zeroPoint) * scale;
  }

  /// Decode regression boxes to corner format
  List<List<double>> _decodeBoxes(List<List<double>> regression) {
    final decodedBoxes = <List<double>>[];
    
    for (int i = 0; i < regression.length; i++) {
      final reg = regression[i];
      final anchorXY = _anchorsXY[i];
      final anchorWH = _anchorsWH[i];
      
      // Decode center coordinates
      final centerX = reg[0] * _centerVariance * anchorWH[0] + anchorXY[0];
      final centerY = reg[1] * _centerVariance * anchorWH[1] + anchorXY[1];
      
      // Decode width and height
      final width = math.exp(reg[2] * _sizeVariance) * anchorWH[0] / 2;
      final height = math.exp(reg[3] * _sizeVariance) * anchorWH[1] / 2;
      
      // Convert center format to corner format
      final x1 = (centerX - width).clamp(0.0, 1.0);
      final y1 = (centerY - height).clamp(0.0, 1.0);
      final x2 = (centerX + width).clamp(0.0, 1.0);
      final y2 = (centerY + height).clamp(0.0, 1.0);
      
      decodedBoxes.add([x1, y1, x2, y2]);
    }
    
    return decodedBoxes;
  }
  
  /// Apply Non-Maximum Suppression
  List<Recognition> _applyNMS(List<Recognition> detections) {
    if (detections.length <= 1) return detections;
    
    // Sort by confidence score (descending)
    detections.sort((a, b) => b.score.compareTo(a.score));
    
    final keep = <Recognition>[];
    final suppressed = <bool>[];
    
    for (int i = 0; i < detections.length; i++) {
      suppressed.add(false);
    }
    
    for (int i = 0; i < detections.length; i++) {
      if (suppressed[i]) continue;
      
      keep.add(detections[i]);
      
      for (int j = i + 1; j < detections.length; j++) {
        if (suppressed[j]) continue;
        
        final iou = _calculateIoU(detections[i].location, detections[j].location);
        if (iou > _nmsIouThreshold) {
          suppressed[j] = true;
        }
      }
    }
    
    return keep.take(_nmsMaxOutputSize).toList();
  }
  
  /// Calculate Intersection over Union (IoU)
  double _calculateIoU(Rect boxA, Rect boxB) {
    final intersectionArea = _calculateIntersectionArea(boxA, boxB);
    final unionArea = boxA.width * boxA.height + boxB.width * boxB.height - intersectionArea;
    
    return unionArea > 0 ? intersectionArea / unionArea : 0.0;
  }
  
  /// Calculate intersection area between two rectangles
  double _calculateIntersectionArea(Rect boxA, Rect boxB) {
    final x1 = math.max(boxA.left, boxB.left);
    final y1 = math.max(boxA.top, boxB.top);
    final x2 = math.min(boxA.right, boxB.right);
    final y2 = math.min(boxA.bottom, boxB.bottom);
    
    if (x2 <= x1 || y2 <= y1) return 0.0;
    
    return (x2 - x1) * (y2 - y1);
  }

  /// Get performance metrics
  Map<String, dynamic> getPerformanceMetrics() {
    return {
      'modelType': _modelType.displayName,
      'inferenceTime': _inferenceTime,
      'totalTime': _totalTime,
      'confidenceThreshold': _confidenceThreshold,
    };
  }
  
  /// Update confidence threshold
  void updateConfidenceThreshold(double threshold) {
    _confidenceThreshold = threshold.clamp(0.0, 1.0);
  }

  /// Check if detector is ready
  bool get isReady => _isInitialized && !_isProcessing;
  
  /// Get current model type
  FaceDetectionModel get modelType => _modelType;

  /// Dispose resources
  void dispose() {
    _interpreter?.close();
    _isInitialized = false;
  }
}
