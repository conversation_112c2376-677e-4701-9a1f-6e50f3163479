{"BCLjoy_200.emd": {"name": "BCLjoy_200.emd", "category": "bundled", "size": 17916, "size_mb": 0.017086029052734375, "format": ".emd", "purpose": "face_analysis", "description": "Face analysis model (BCL format)", "input_shape": "unknown", "output_shape": "unknown", "usage_guide": "Proprietary format for face analysis", "md5_hash": "29b350119fe38eb8fe6a45aed35c09cc"}, "BCLlefteyeclosed_200.emd": {"name": "BCLlefteyeclosed_200.emd", "category": "bundled", "size": 8630, "size_mb": 0.008230209350585938, "format": ".emd", "purpose": "eye_state_detection", "description": "Eye open/closed state detection", "input_shape": "Eye region (typically 32x32)", "output_shape": "Open/closed probability", "usage_guide": "Detect if eyes are open or closed for liveness detection", "md5_hash": "910b646512a1cb2ee80a2f94d1e0a936"}, "BCLrighteyeclosed_200.emd": {"name": "BCLrighteyeclosed_200.emd", "category": "bundled", "size": 8655, "size_mb": 0.008254051208496094, "format": ".emd", "purpose": "eye_state_detection", "description": "Eye open/closed state detection", "input_shape": "Eye region (typically 32x32)", "output_shape": "Open/closed probability", "usage_guide": "Detect if eyes are open or closed for liveness detection", "md5_hash": "bc1b05090c86a1213f6720cd502877a5"}, "blazeface.tfl": {"name": "blazeface.tfl", "category": "bundled", "size": 347928, "size_mb": 0.33180999755859375, "format": ".tfl", "purpose": "face_detection", "description": "Google BlazeFace model for real-time face detection", "input_shape": "128x128x3 or 256x256x3", "output_shape": "Face bounding boxes + landmarks", "usage_guide": "Use for fast face detection in real-time applications", "md5_hash": "f25fb5752634ba2183d9a16fa878f60a"}, "contours.tfl": {"name": "contours.tfl", "category": "bundled", "size": 1124144, "size_mb": 1.0720672607421875, "format": ".tfl", "purpose": "face_landmarks", "description": "Face contour and landmark detection model", "input_shape": "Face region (typically 192x192x3)", "output_shape": "468 face landmarks (x,y coordinates)", "usage_guide": "Apply after face detection to get detailed facial landmarks", "md5_hash": "244f99bbd9ebf2d57a9e79534d1c4ce1"}, "fssd_25_8bit_gray_v2.tflite": {"name": "fssd_25_8bit_gray_v2.tflite", "category": "bundled", "size": 231816, "size_mb": 0.22107696533203125, "format": ".tflite", "purpose": "object_detection", "description": "Feature-fused Single Shot Detector for object detection", "input_shape": "Variable (typically 320x320x3)", "output_shape": "Object bounding boxes + classes + scores", "usage_guide": "General object detection, can detect faces and other objects", "md5_hash": "2e1eb21ed481ef37137ae151c01350e3", "tflite_format": true, "framework": "TensorFlow Lite", "quantized": false}, "fssd_25_8bit_v2.tflite": {"name": "fssd_25_8bit_v2.tflite", "category": "bundled", "size": 232096, "size_mb": 0.221343994140625, "format": ".tflite", "purpose": "object_detection", "description": "Feature-fused Single Shot Detector for object detection", "input_shape": "Variable (typically 320x320x3)", "output_shape": "Object bounding boxes + classes + scores", "usage_guide": "General object detection, can detect faces and other objects", "md5_hash": "c063dbbfb3b4b2ad495fd9e4e9ada72f", "tflite_format": true, "framework": "TensorFlow Lite", "quantized": false}, "fssd_anchors_v2.pb": {"name": "fssd_anchors_v2.pb", "category": "bundled", "size": 146, "size_mb": 0.0001392364501953125, "format": ".pb", "purpose": "object_detection", "description": "Feature-fused Single Shot Detector for object detection", "input_shape": "Variable (typically 320x320x3)", "output_shape": "Object bounding boxes + classes + scores", "usage_guide": "General object detection, can detect faces and other objects", "md5_hash": "0378acddee991b12cf3116563b16d036"}, "fssd_anchors_v5.pb": {"name": "fssd_anchors_v5.pb", "category": "bundled", "size": 144, "size_mb": 0.0001373291015625, "format": ".pb", "purpose": "object_detection", "description": "Feature-fused Single Shot Detector for object detection", "input_shape": "Variable (typically 320x320x3)", "output_shape": "Object bounding boxes + classes + scores", "usage_guide": "General object detection, can detect faces and other objects", "md5_hash": "3dbe45f006222c709ac428150c2b5d02"}, "fssd_medium_8bit_gray_v5.tflite": {"name": "fssd_medium_8bit_gray_v5.tflite", "category": "bundled", "size": 613080, "size_mb": 0.5846786499023438, "format": ".tflite", "purpose": "object_detection", "description": "Feature-fused Single Shot Detector for object detection", "input_shape": "Variable (typically 320x320x3)", "output_shape": "Object bounding boxes + classes + scores", "usage_guide": "General object detection, can detect faces and other objects", "md5_hash": "2963e017d28f3037d655cdd5e2235759", "tflite_format": true, "framework": "TensorFlow Lite", "quantized": false}, "fssd_medium_8bit_v5.tflite": {"name": "fssd_medium_8bit_v5.tflite", "category": "bundled", "size": 613352, "size_mb": 0.5849380493164062, "format": ".tflite", "purpose": "object_detection", "description": "Feature-fused Single Shot Detector for object detection", "input_shape": "Variable (typically 320x320x3)", "output_shape": "Object bounding boxes + classes + scores", "usage_guide": "General object detection, can detect faces and other objects", "md5_hash": "4b0bce4d343cfa7ca3015c3efd638946", "tflite_format": true, "framework": "TensorFlow Lite", "quantized": false}, "LMprec_600.emd": {"name": "LMprec_600.emd", "category": "bundled", "size": 1078354, "size_mb": 1.0283985137939453, "format": ".emd", "purpose": "landmark_precision", "description": "High-precision landmark detection model", "input_shape": "Face region", "output_shape": "Precise facial landmarks", "usage_guide": "Get high-precision facial landmarks for face alignment", "md5_hash": "ca3498529427c3afd43345c431c00549"}, "MFT_fssd_accgray.pb": {"name": "MFT_fssd_accgray.pb", "category": "bundled", "size": 793, "size_mb": 0.0007562637329101562, "format": ".pb", "purpose": "object_detection", "description": "Feature-fused Single Shot Detector for object detection", "input_shape": "Variable (typically 320x320x3)", "output_shape": "Object bounding boxes + classes + scores", "usage_guide": "General object detection, can detect faces and other objects", "md5_hash": "45e2a40a72a74da0de1721fa0ef36f7f"}, "MFT_fssd_fastgray.pb": {"name": "MFT_fssd_fastgray.pb", "category": "bundled", "size": 792, "size_mb": 0.00075531005859375, "format": ".pb", "purpose": "object_detection", "description": "Feature-fused Single Shot Detector for object detection", "input_shape": "Variable (typically 320x320x3)", "output_shape": "Object bounding boxes + classes + scores", "usage_guide": "General object detection, can detect faces and other objects", "md5_hash": "017b50bcc9434d19bf44b9ebc49530b6"}, "barcode_ssd_mobilenet_v1_dmp25_quant.tflite": {"name": "barcode_ssd_mobilenet_v1_dmp25_quant.tflite", "category": "mlkit", "size": 390456, "size_mb": 0.37236785888671875, "format": ".tflite", "purpose": "barcode_detection", "description": "Barcode and QR code detection model", "input_shape": "Variable image size", "output_shape": "Barcode bounding boxes", "usage_guide": "Detect barcodes/QR codes in images before decoding", "md5_hash": "8e582df04920a34c3391404146b3d6b1", "tflite_format": true, "framework": "TensorFlow Lite", "quantized": true}, "oned_auto_regressor_mobile.tflite": {"name": "oned_auto_regressor_mobile.tflite", "category": "mlkit", "size": 213880, "size_mb": 0.20397186279296875, "format": ".tflite", "purpose": "barcode_regression", "description": "1D barcode auto-regressor for decoding", "input_shape": "Barcode features", "output_shape": "Decoded barcode text", "usage_guide": "Decode 1D barcodes from extracted features", "md5_hash": "8d60144f8069c12b910a3b5d3c764e81", "tflite_format": true, "framework": "TensorFlow Lite", "quantized": false}, "oned_feature_extractor_mobile.tflite": {"name": "oned_feature_extractor_mobile.tflite", "category": "mlkit", "size": 276552, "size_mb": 0.26374053955078125, "format": ".tflite", "purpose": "barcode_feature_extraction", "description": "1D barcode feature extraction model", "input_shape": "Barcode region", "output_shape": "Feature vectors", "usage_guide": "Extract features from detected 1D barcodes", "md5_hash": "bdfb83dee2429c447fc01e772906189d", "tflite_format": true, "framework": "TensorFlow Lite", "quantized": false}}