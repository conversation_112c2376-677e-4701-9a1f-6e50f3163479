import 'dart:io';
import 'package:tflite_flutter/tflite_flutter.dart';

void main() async {
  print('Testing model output shapes...');
  
  try {
    final interpreter = await Interpreter.fromAsset('assets/models/ssd_mobilenet.tflite');
    
    print('\n=== MODEL INFO ===');
    print('Input tensors: ${interpreter.getInputTensors().length}');
    print('Output tensors: ${interpreter.getOutputTensors().length}');
    
    print('\n=== OUTPUT TENSORS ===');
    for (int i = 0; i < interpreter.getOutputTensors().length; i++) {
      final tensor = interpreter.getOutputTensor(i);
      print('Output $i: ${tensor.shape} (${tensor.type})');
    }
    
    // Test with dummy input
    print('\n=== TESTING INFERENCE ===');
    final input = [List.generate(300, (i) => List.generate(300, (j) => List.generate(3, (k) => 0.0)))];
    
    // Create output tensors based on actual model output
    final output = {
      0: [List<num>.filled(10, 0)],                              // Scores [1, 10]
      1: [List<List<num>>.filled(10, List<num>.filled(4, 0))],   // Locations [1, 10, 4]
      2: [0.0],                                                  // Number of detections [1]
      3: [List<num>.filled(10, 0)],                              // Classes [1, 10]
    };
    
    interpreter.runForMultipleInputs([input], output);
    
    print('✅ Inference successful!');
    print('Output 0 (Scores): ${output[0]![0].length} elements');
    print('Output 1 (Locations): ${output[1]![0].length} boxes with ${output[1]![0][0].length} coordinates each');
    print('Output 2 (Num detections): ${output[2]![0]}');
    print('Output 3 (Classes): ${output[3]![0].length} elements');
    
    interpreter.close();
    print('\n✅ Model test completed successfully!');
    
  } catch (e) {
    print('❌ Error: $e');
  }
}
