#!/usr/bin/env python3
"""
Model Quantization Script for BlazeFace
Converts FP32 model to INT8 for faster inference on RK3399
"""

import tensorflow as tf
import numpy as np
import os

def quantize_blazeface_model():
    """Convert BlazeFace model to INT8 quantized version"""
    
    # Path to original model
    original_model_path = "../assets/civams_models/blazeface.tfl"
    quantized_model_path = "../assets/civams_models/blazeface_int8.tfl"
    
    print("🔄 Starting BlazeFace model quantization...")
    print(f"Input model: {original_model_path}")
    print(f"Output model: {quantized_model_path}")
    
    try:
        # Load the original model
        interpreter = tf.lite.Interpreter(model_path=original_model_path)
        interpreter.allocate_tensors()
        
        # Get input details
        input_details = interpreter.get_input_details()
        output_details = interpreter.get_output_details()
        
        print(f"✅ Original model loaded successfully")
        print(f"Input shape: {input_details[0]['shape']}")
        print(f"Input type: {input_details[0]['dtype']}")
        
        # Create representative dataset for quantization
        def representative_data_gen():
            """Generate representative data for quantization"""
            input_shape = input_details[0]['shape']
            batch_size, height, width, channels = input_shape
            
            # Generate random data that represents typical face images
            for _ in range(100):  # 100 samples for calibration
                # Generate realistic image data (0-1 range for normalized images)
                data = np.random.random((batch_size, height, width, channels)).astype(np.float32)
                # Add some structure to make it more realistic
                data = data * 0.8 + 0.1  # Scale to 0.1-0.9 range
                yield [data]
        
        # Configure quantization
        converter = tf.lite.TFLiteConverter.from_saved_model_dir("temp_saved_model")
        
        # Alternative: Load from .tflite file and re-quantize
        # This approach works better for existing .tflite models
        print("🔄 Setting up quantization parameters...")
        
        # Enable quantization optimizations
        converter.optimizations = [tf.lite.Optimize.DEFAULT]
        
        # Set target spec for INT8
        converter.target_spec.supported_types = [tf.int8]
        
        # Use representative dataset
        converter.representative_dataset = representative_data_gen
        
        # Ensure all ops are quantized
        converter.inference_input_type = tf.int8
        converter.inference_output_type = tf.int8
        
        print("🔄 Converting model to INT8...")
        quantized_tflite_model = converter.convert()
        
        # Save quantized model
        with open(quantized_model_path, 'wb') as f:
            f.write(quantized_tflite_model)
        
        print(f"✅ Quantized model saved: {quantized_model_path}")
        
        # Compare model sizes
        original_size = os.path.getsize(original_model_path)
        quantized_size = os.path.getsize(quantized_model_path)
        size_reduction = (1 - quantized_size / original_size) * 100
        
        print(f"📊 Model size comparison:")
        print(f"  Original: {original_size / 1024:.1f} KB")
        print(f"  Quantized: {quantized_size / 1024:.1f} KB")
        print(f"  Size reduction: {size_reduction:.1f}%")
        
        # Test quantized model
        test_quantized_model(quantized_model_path)
        
    except Exception as e:
        print(f"❌ Quantization failed: {e}")
        print("Trying alternative quantization method...")
        quantize_existing_tflite_model(original_model_path, quantized_model_path)

def quantize_existing_tflite_model(input_path, output_path):
    """Alternative method to quantize existing .tflite model"""
    try:
        print("🔄 Using alternative quantization method...")
        
        # Load existing model
        with open(input_path, 'rb') as f:
            model_content = f.read()
        
        # Create converter from model content
        converter = tf.lite.TFLiteConverter.from_buffer(model_content)
        
        # Enable quantization
        converter.optimizations = [tf.lite.Optimize.DEFAULT]
        
        # Convert
        quantized_model = converter.convert()
        
        # Save
        with open(output_path, 'wb') as f:
            f.write(quantized_model)
        
        print(f"✅ Alternative quantization successful: {output_path}")
        
    except Exception as e:
        print(f"❌ Alternative quantization also failed: {e}")
        print("Manual quantization may be required")

def test_quantized_model(model_path):
    """Test the quantized model to ensure it works"""
    try:
        print("🔄 Testing quantized model...")
        
        interpreter = tf.lite.Interpreter(model_path=model_path)
        interpreter.allocate_tensors()
        
        input_details = interpreter.get_input_details()
        output_details = interpreter.get_output_details()
        
        print(f"✅ Quantized model test successful")
        print(f"Input type: {input_details[0]['dtype']}")
        print(f"Output type: {output_details[0]['dtype']}")
        
        # Test inference speed
        input_shape = input_details[0]['shape']
        test_input = np.random.random(input_shape).astype(input_details[0]['dtype'])
        
        import time
        start_time = time.time()
        
        interpreter.set_tensor(input_details[0]['index'], test_input)
        interpreter.invoke()
        output = interpreter.get_tensor(output_details[0]['index'])
        
        inference_time = (time.time() - start_time) * 1000
        print(f"⚡ Test inference time: {inference_time:.1f}ms")
        
    except Exception as e:
        print(f"❌ Quantized model test failed: {e}")

if __name__ == "__main__":
    quantize_blazeface_model()
