# PowerShell script to build APK for Live Object Detection SSD MobileNet
# Run this script from PowerShell with: .\build_apk.ps1

Write-Host "Building APK for Live Object Detection SSD MobileNet..." -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Set location to script directory
Set-Location $PSScriptRoot

Write-Host "`nStep 1: Checking Flutter installation..." -ForegroundColor Yellow
try {
    $flutterVersion = flutter --version
    Write-Host "Flutter is installed and accessible" -ForegroundColor Green
} catch {
    Write-Host "Error: Flutter is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

Write-Host "`nStep 2: Running flutter doctor..." -ForegroundColor Yellow
flutter doctor

Write-Host "`nStep 3: Getting dependencies..." -ForegroundColor Yellow
flutter pub get

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error getting dependencies" -ForegroundColor Red
    exit 1
}

Write-Host "`nStep 4: Cleaning previous builds and cache..." -ForegroundColor Yellow
flutter clean
flutter pub cache clean

Write-Host "`nStep 5: Building APK (Release mode)..." -ForegroundColor Yellow
flutter build apk --release

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nBuild completed successfully!" -ForegroundColor Green
    Write-Host "APK location: build\app\outputs\flutter-apk\app-release.apk" -ForegroundColor Green
    
    # Check if APK file exists
    $apkPath = "build\app\outputs\flutter-apk\app-release.apk"
    if (Test-Path $apkPath) {
        $apkSize = (Get-Item $apkPath).Length / 1MB
        Write-Host "APK size: $([math]::Round($apkSize, 2)) MB" -ForegroundColor Green
    }
} else {
    Write-Host "`nBuild failed!" -ForegroundColor Red
    Write-Host "Try running the following commands manually:" -ForegroundColor Yellow
    Write-Host "1. flutter clean" -ForegroundColor White
    Write-Host "2. flutter pub get" -ForegroundColor White
    Write-Host "3. flutter build apk --release" -ForegroundColor White
}

Write-Host "`nPress any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
