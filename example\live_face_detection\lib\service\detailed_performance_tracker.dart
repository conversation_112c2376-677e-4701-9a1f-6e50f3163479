// Detailed Performance Tracking Service
import 'dart:collection';

/// Detailed performance tracker for face detection pipeline
class DetailedPerformanceTracker {
  // Performance metrics storage
  static final Map<String, Queue<int>> _timings = {};
  static final Map<String, int> _counters = {};
  static DateTime _lastResetTime = DateTime.now();
  
  // Camera FPS tracking
  static DateTime _lastCameraFrame = DateTime.now();
  static final Queue<DateTime> _cameraFrameTimes = Queue<DateTime>();
  static double _cameraFPS = 0.0;
  
  // Processing FPS tracking  
  static DateTime _lastProcessFrame = DateTime.now();
  static final Queue<DateTime> _processFrameTimes = Queue<DateTime>();
  static double _processingFPS = 0.0;
  
  // Pipeline breakdown timings
  static int _lastImageConversionTime = 0;
  static int _lastImageResizeTime = 0;
  static int _lastTensorCreationTime = 0;
  static int _lastInferenceTime = 0;
  static int _lastPostProcessingTime = 0;
  static int _lastTotalPipelineTime = 0;
  
  // Detection results tracking
  static int _totalFramesReceived = 0;
  static int _totalFramesProcessed = 0;
  static int _totalFacesDetected = 0;
  static final Queue<int> _recentDetectionCounts = Queue<int>();
  
  /// Record camera frame received
  static void recordCameraFrame() {
    final now = DateTime.now();
    _lastCameraFrame = now;
    _cameraFrameTimes.add(now);
    _totalFramesReceived++;
    
    // Keep only last 30 frames for FPS calculation
    while (_cameraFrameTimes.length > 30) {
      _cameraFrameTimes.removeFirst();
    }
    
    // Calculate camera FPS
    if (_cameraFrameTimes.length >= 2) {
      final duration = _cameraFrameTimes.last.difference(_cameraFrameTimes.first);
      _cameraFPS = (_cameraFrameTimes.length - 1) * 1000 / duration.inMilliseconds;
    }
  }
  
  /// Record processing frame start
  static void recordProcessingStart() {
    final now = DateTime.now();
    _lastProcessFrame = now;
    _processFrameTimes.add(now);
    _totalFramesProcessed++;
    
    // Keep only last 20 frames for processing FPS
    while (_processFrameTimes.length > 20) {
      _processFrameTimes.removeFirst();
    }
    
    // Calculate processing FPS
    if (_processFrameTimes.length >= 2) {
      final duration = _processFrameTimes.last.difference(_processFrameTimes.first);
      _processingFPS = (_processFrameTimes.length - 1) * 1000 / duration.inMilliseconds;
    }
  }
  
  /// Record pipeline step timing
  static void recordStepTime(String step, int timeMs) {
    _timings.putIfAbsent(step, () => Queue<int>());
    final queue = _timings[step]!;
    queue.add(timeMs);
    
    // Keep only last 20 measurements
    while (queue.length > 20) {
      queue.removeFirst();
    }
    
    // Update individual step timings
    switch (step) {
      case 'image_conversion':
        _lastImageConversionTime = timeMs;
        break;
      case 'image_resize':
        _lastImageResizeTime = timeMs;
        break;
      case 'tensor_creation':
        _lastTensorCreationTime = timeMs;
        break;
      case 'inference':
        _lastInferenceTime = timeMs;
        break;
      case 'post_processing':
        _lastPostProcessingTime = timeMs;
        break;
      case 'total_pipeline':
        _lastTotalPipelineTime = timeMs;
        break;
    }
  }
  
  /// Record detection results
  static void recordDetectionResults(int faceCount) {
    _totalFacesDetected += faceCount;
    _recentDetectionCounts.add(faceCount);
    
    // Keep only last 20 detection counts
    while (_recentDetectionCounts.length > 20) {
      _recentDetectionCounts.removeFirst();
    }
  }
  
  /// Get average timing for a step
  static double getAverageTime(String step) {
    final queue = _timings[step];
    if (queue == null || queue.isEmpty) return 0.0;
    
    final sum = queue.reduce((a, b) => a + b);
    return sum / queue.length;
  }
  
  /// Get current performance snapshot
  static PerformanceSnapshot getCurrentSnapshot() {
    return PerformanceSnapshot(
      // FPS metrics
      cameraFPS: _cameraFPS,
      processingFPS: _processingFPS,
      
      // Pipeline breakdown (current)
      imageConversionTime: _lastImageConversionTime,
      imageResizeTime: _lastImageResizeTime,
      tensorCreationTime: _lastTensorCreationTime,
      inferenceTime: _lastInferenceTime,
      postProcessingTime: _lastPostProcessingTime,
      totalPipelineTime: _lastTotalPipelineTime,
      
      // Pipeline breakdown (average)
      avgImageConversionTime: getAverageTime('image_conversion'),
      avgImageResizeTime: getAverageTime('image_resize'),
      avgTensorCreationTime: getAverageTime('tensor_creation'),
      avgInferenceTime: getAverageTime('inference'),
      avgPostProcessingTime: getAverageTime('post_processing'),
      avgTotalPipelineTime: getAverageTime('total_pipeline'),
      
      // Detection metrics
      totalFramesReceived: _totalFramesReceived,
      totalFramesProcessed: _totalFramesProcessed,
      totalFacesDetected: _totalFacesDetected,
      recentAverageFaces: _recentDetectionCounts.isEmpty 
        ? 0.0 
        : _recentDetectionCounts.reduce((a, b) => a + b) / _recentDetectionCounts.length,
      
      // Efficiency metrics
      processingRatio: _totalFramesReceived > 0 
        ? _totalFramesProcessed / _totalFramesReceived 
        : 0.0,
      uptime: DateTime.now().difference(_lastResetTime),
    );
  }
  
  /// Print detailed performance report
  static void printDetailedReport() {
    final snapshot = getCurrentSnapshot();
    
    print('\n' + '=' * 60);
    print('📊 DETAILED PERFORMANCE REPORT');
    print('=' * 60);
    
    // FPS Section
    print('\n🎥 FRAME RATE METRICS:');
    print('  Camera FPS:     ${snapshot.cameraFPS.toStringAsFixed(1)} fps');
    print('  Processing FPS: ${snapshot.processingFPS.toStringAsFixed(1)} fps');
    print('  Processing Ratio: ${(snapshot.processingRatio * 100).toStringAsFixed(1)}%');
    
    // Pipeline Breakdown - Current
    print('\n⚡ CURRENT PIPELINE BREAKDOWN:');
    print('  Image Conversion: ${snapshot.imageConversionTime}ms');
    print('  Image Resize:     ${snapshot.imageResizeTime}ms');
    print('  Tensor Creation:  ${snapshot.tensorCreationTime}ms');
    print('  Model Inference:  ${snapshot.inferenceTime}ms');
    print('  Post Processing:  ${snapshot.postProcessingTime}ms');
    print('  ─────────────────────────────────');
    print('  Total Pipeline:   ${snapshot.totalPipelineTime}ms');
    
    // Pipeline Breakdown - Average
    print('\n📈 AVERAGE PIPELINE BREAKDOWN:');
    print('  Image Conversion: ${snapshot.avgImageConversionTime.toStringAsFixed(1)}ms');
    print('  Image Resize:     ${snapshot.avgImageResizeTime.toStringAsFixed(1)}ms');
    print('  Tensor Creation:  ${snapshot.avgTensorCreationTime.toStringAsFixed(1)}ms');
    print('  Model Inference:  ${snapshot.avgInferenceTime.toStringAsFixed(1)}ms');
    print('  Post Processing:  ${snapshot.avgPostProcessingTime.toStringAsFixed(1)}ms');
    print('  ─────────────────────────────────');
    print('  Total Pipeline:   ${snapshot.avgTotalPipelineTime.toStringAsFixed(1)}ms');
    
    // Bottleneck Analysis
    print('\n🔍 BOTTLENECK ANALYSIS:');
    final bottlenecks = _identifyBottlenecks(snapshot);
    for (final bottleneck in bottlenecks) {
      print('  ${bottleneck.severity} ${bottleneck.step}: ${bottleneck.time.toStringAsFixed(1)}ms (${bottleneck.percentage.toStringAsFixed(1)}%)');
    }
    
    // Detection Metrics
    print('\n🎯 DETECTION METRICS:');
    print('  Total Frames Received: ${snapshot.totalFramesReceived}');
    print('  Total Frames Processed: ${snapshot.totalFramesProcessed}');
    print('  Total Faces Detected: ${snapshot.totalFacesDetected}');
    print('  Average Faces/Frame: ${snapshot.recentAverageFaces.toStringAsFixed(1)}');
    
    // System Info
    print('\n⏱️ SYSTEM INFO:');
    print('  Uptime: ${_formatDuration(snapshot.uptime)}');
    print('  Last Update: ${DateTime.now().toString().substring(11, 19)}');
    
    print('=' * 60);
  }
  
  /// Identify performance bottlenecks
  static List<BottleneckInfo> _identifyBottlenecks(PerformanceSnapshot snapshot) {
    final steps = [
      BottleneckInfo('Image Conversion', snapshot.avgImageConversionTime, snapshot.avgTotalPipelineTime),
      BottleneckInfo('Image Resize', snapshot.avgImageResizeTime, snapshot.avgTotalPipelineTime),
      BottleneckInfo('Tensor Creation', snapshot.avgTensorCreationTime, snapshot.avgTotalPipelineTime),
      BottleneckInfo('Model Inference', snapshot.avgInferenceTime, snapshot.avgTotalPipelineTime),
      BottleneckInfo('Post Processing', snapshot.avgPostProcessingTime, snapshot.avgTotalPipelineTime),
    ];
    
    // Sort by time descending
    steps.sort((a, b) => b.time.compareTo(a.time));
    
    // Assign severity levels
    for (int i = 0; i < steps.length; i++) {
      if (steps[i].percentage > 40) {
        steps[i].severity = '🔴 CRITICAL';
      } else if (steps[i].percentage > 25) {
        steps[i].severity = '🟡 HIGH';
      } else if (steps[i].percentage > 15) {
        steps[i].severity = '🟠 MEDIUM';
      } else {
        steps[i].severity = '🟢 LOW';
      }
    }
    
    return steps;
  }
  
  /// Format duration for display
  static String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }
  
  /// Reset all metrics
  static void reset() {
    _timings.clear();
    _counters.clear();
    _cameraFrameTimes.clear();
    _processFrameTimes.clear();
    _recentDetectionCounts.clear();
    
    _totalFramesReceived = 0;
    _totalFramesProcessed = 0;
    _totalFacesDetected = 0;
    _cameraFPS = 0.0;
    _processingFPS = 0.0;
    
    _lastResetTime = DateTime.now();
  }
  
  /// Start periodic reporting (every 15 seconds to reduce overhead)
  static void startPeriodicReporting() {
    Stream.periodic(const Duration(seconds: 15)).listen((_) {
      printDetailedReport();
    });
  }
}

/// Performance snapshot data class
class PerformanceSnapshot {
  final double cameraFPS;
  final double processingFPS;
  
  final int imageConversionTime;
  final int imageResizeTime;
  final int tensorCreationTime;
  final int inferenceTime;
  final int postProcessingTime;
  final int totalPipelineTime;
  
  final double avgImageConversionTime;
  final double avgImageResizeTime;
  final double avgTensorCreationTime;
  final double avgInferenceTime;
  final double avgPostProcessingTime;
  final double avgTotalPipelineTime;
  
  final int totalFramesReceived;
  final int totalFramesProcessed;
  final int totalFacesDetected;
  final double recentAverageFaces;
  final double processingRatio;
  final Duration uptime;
  
  PerformanceSnapshot({
    required this.cameraFPS,
    required this.processingFPS,
    required this.imageConversionTime,
    required this.imageResizeTime,
    required this.tensorCreationTime,
    required this.inferenceTime,
    required this.postProcessingTime,
    required this.totalPipelineTime,
    required this.avgImageConversionTime,
    required this.avgImageResizeTime,
    required this.avgTensorCreationTime,
    required this.avgInferenceTime,
    required this.avgPostProcessingTime,
    required this.avgTotalPipelineTime,
    required this.totalFramesReceived,
    required this.totalFramesProcessed,
    required this.totalFacesDetected,
    required this.recentAverageFaces,
    required this.processingRatio,
    required this.uptime,
  });
}

/// Bottleneck information
class BottleneckInfo {
  final String step;
  final double time;
  final double percentage;
  String severity = '';
  
  BottleneckInfo(this.step, this.time, double totalTime) 
    : percentage = totalTime > 0 ? (time / totalTime) * 100 : 0;
}
