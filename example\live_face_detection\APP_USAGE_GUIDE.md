# Live Object Detection App - Usage Guide

## 📱 App Overview

This app performs **real-time object detection** using your device's camera. It can detect and identify 8 different food items with live bounding boxes and confidence scores.

## 🍽️ Detectable Food Items

The app can detect these food categories:
1. **French Fries** 🍟
2. **Sausage** 🌭
3. **Grilled Chicken** 🍗
4. **Fish** 🐟
5. **Scrambled Egg** 🍳
6. **Pasta** 🍝
7. **Lettuce** 🥬
8. **Cantaloupe** 🍈

## 🚀 How to Use

### 1. Installation
- Install the APK: `app-release.apk` (81.9MB)
- Grant **Camera** and **Storage** permissions when prompted

### 2. Launch the App
- Open "Live Object Detection SSD MobileNet" from your app drawer
- The app will request camera permission - tap **Allow**

### 3. Real-time Detection
- **Point your camera** at food items from the list above
- **Hold steady** for 1-2 seconds for best detection
- **Green bounding boxes** will appear around detected objects
- **Labels and confidence scores** will be displayed

### 4. Best Practices for Detection

#### Lighting Conditions
- ✅ **Good lighting**: Natural daylight or bright indoor lighting
- ❌ **Avoid**: Dark environments, backlighting, harsh shadows

#### Camera Distance
- ✅ **Optimal**: 20-50cm from the object
- ❌ **Too close**: Less than 10cm
- ❌ **Too far**: More than 1 meter

#### Object Positioning
- ✅ **Clear view**: Object fully visible in frame
- ✅ **Stable**: Hold camera steady
- ❌ **Avoid**: Partial objects, moving too fast

#### Background
- ✅ **Simple backgrounds**: Plain surfaces work best
- ❌ **Cluttered**: Too many objects in frame

## 📊 Understanding the Results

### Bounding Boxes
- **Green rectangles** around detected objects
- **Size** indicates the detected area
- **Multiple boxes** for multiple objects

### Labels
- **Object name** (e.g., "french_fries")
- **Confidence score** (0.0 to 1.0)
- **Higher scores** = more confident detection

### Performance Stats
The app displays real-time performance metrics:
- **Conversion time**: Camera image processing
- **Pre-processing time**: Image preparation
- **Inference time**: AI model processing
- **Total prediction time**: Complete detection cycle
- **Frame size**: Camera resolution

## 🔧 Troubleshooting

### No Detection Results
1. **Check lighting** - ensure good illumination
2. **Move closer** - get within optimal range (20-50cm)
3. **Hold steady** - avoid camera shake
4. **Try different angles** - some angles work better
5. **Check object type** - ensure it's in the supported list

### App Performance Issues
1. **Close other apps** - free up memory
2. **Restart the app** - clear any temporary issues
3. **Check device temperature** - avoid overheating
4. **Ensure good lighting** - poor light affects performance

### Camera Issues
1. **Grant permissions** - check app permissions in settings
2. **Restart app** - close and reopen
3. **Check camera** - test with other camera apps
4. **Clean lens** - ensure camera lens is clean

## 📈 Performance Tips

### For Best Speed
- Use in **good lighting** conditions
- Keep **background simple**
- **Hold camera steady**
- **Close other apps** running in background

### For Best Accuracy
- **Good lighting** is crucial
- **Clear, unobstructed view** of objects
- **Appropriate distance** (20-50cm)
- **Wait 1-2 seconds** for processing

## 🎯 Use Cases

### Food Recognition
- **Meal logging**: Identify food items for diet tracking
- **Cooking assistance**: Recognize ingredients while cooking
- **Educational**: Learn about different food types

### Testing & Demo
- **AI demonstration**: Show real-time object detection
- **Performance testing**: Measure inference speed
- **Model evaluation**: Test detection accuracy

## 📱 Technical Details

### Model Information
- **Architecture**: SSD MobileNet V2
- **Input Size**: 300x300 pixels
- **Output**: Bounding boxes, classes, confidence scores
- **Inference Time**: ~50-200ms per frame

### Device Requirements
- **Android**: API level 26+ (Android 8.0+)
- **RAM**: Minimum 3GB recommended
- **Storage**: 100MB free space
- **Camera**: Rear camera with autofocus

### Privacy
- **No data collection**: All processing happens on-device
- **No internet required**: Works completely offline
- **No image storage**: Images are processed in real-time only

## 🆘 Support

If you encounter issues:
1. Check this guide first
2. Ensure your device meets requirements
3. Try the troubleshooting steps above
4. Test with different food items and lighting conditions

**Remember**: This is a demonstration app optimized for the 8 specific food categories listed above. Detection accuracy may vary based on lighting, angle, and object appearance.
