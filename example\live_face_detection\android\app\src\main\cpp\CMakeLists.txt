cmake_minimum_required(VERSION 3.22.1)

project("yuv_neon_converter")

# Enable ARM NEON optimizations
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -mfpu=neon -mfloat-abi=hard")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -mfpu=neon -mfloat-abi=hard")

# Add ARM NEON support
add_definitions(-D__ARM_NEON__)

# Create shared library
add_library(yuv_neon_converter SHARED
    yuv_neon_converter.cpp
)

# Link against log library
find_library(log-lib log)
target_link_libraries(yuv_neon_converter ${log-lib})
