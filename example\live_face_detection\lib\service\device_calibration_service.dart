// Device-Specific Coordinate Calibration Service
import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Device characteristics and calibration parameters
class DeviceProfile {
  final String deviceId;
  final String deviceModel;
  final Size screenSize;
  final double pixelRatio;
  final Orientation orientation;
  final double scaleFactorX;
  final double scaleFactorY;
  final double offsetX;
  final double offsetY;
  final double rotation; // Rotation in degrees
  final DateTime lastCalibrated;

  DeviceProfile({
    required this.deviceId,
    required this.deviceModel,
    required this.screenSize,
    required this.pixelRatio,
    required this.orientation,
    this.scaleFactorX = 1.0,
    this.scaleFactorY = 1.0,
    this.offsetX = 0.0,
    this.offsetY = 0.0,
    this.rotation = -90.0, // Default -90° to align red box with screen
    DateTime? lastCalibrated,
  }) : lastCalibrated = lastCalibrated ?? DateTime.now();

  Map<String, dynamic> toJson() => {
    'deviceId': deviceId,
    'deviceModel': deviceModel,
    'screenWidth': screenSize.width,
    'screenHeight': screenSize.height,
    'pixelRatio': pixelRatio,
    'orientation': orientation.index,
    'scaleFactorX': scaleFactorX,
    'scaleFactorY': scaleFactorY,
    'offsetX': offsetX,
    'offsetY': offsetY,
    'rotation': rotation,
    'lastCalibrated': lastCalibrated.millisecondsSinceEpoch,
  };

  factory DeviceProfile.fromJson(Map<String, dynamic> json) => DeviceProfile(
    deviceId: json['deviceId'],
    deviceModel: json['deviceModel'],
    screenSize: Size(json['screenWidth'], json['screenHeight']),
    pixelRatio: json['pixelRatio'],
    orientation: Orientation.values[json['orientation']],
    scaleFactorX: json['scaleFactorX'] ?? 1.0,
    scaleFactorY: json['scaleFactorY'] ?? 1.0,
    offsetX: json['offsetX'] ?? 0.0,
    offsetY: json['offsetY'] ?? 0.0,
    rotation: json['rotation'] ?? 0.0,
    lastCalibrated: DateTime.fromMillisecondsSinceEpoch(json['lastCalibrated']),
  );

  DeviceProfile copyWith({
    double? scaleFactorX,
    double? scaleFactorY,
    double? offsetX,
    double? offsetY,
    double? rotation,
  }) => DeviceProfile(
    deviceId: deviceId,
    deviceModel: deviceModel,
    screenSize: screenSize,
    pixelRatio: pixelRatio,
    orientation: orientation,
    scaleFactorX: scaleFactorX ?? this.scaleFactorX,
    scaleFactorY: scaleFactorY ?? this.scaleFactorY,
    offsetX: offsetX ?? this.offsetX,
    offsetY: offsetY ?? this.offsetY,
    rotation: rotation ?? this.rotation,
  );
}

/// Coordinate transformation parameters
class CoordinateTransform {
  final double scaleX;
  final double scaleY;
  final double offsetX;
  final double offsetY;
  final double rotation; // Rotation in degrees
  final Size sourceSize;
  final Size targetSize;

  CoordinateTransform({
    required this.scaleX,
    required this.scaleY,
    required this.offsetX,
    required this.offsetY,
    this.rotation = 0.0,
    required this.sourceSize,
    required this.targetSize,
  });

  Rect transformRect(Rect rect) {
    // Apply scaling first
    double scaledLeft = rect.left * scaleX;
    double scaledTop = rect.top * scaleY;
    double scaledRight = rect.right * scaleX;
    double scaledBottom = rect.bottom * scaleY;

    // Apply rotation if needed
    if (rotation != 0.0) {
      final centerX = (scaledLeft + scaledRight) / 2;
      final centerY = (scaledTop + scaledBottom) / 2;
      final width = scaledRight - scaledLeft;
      final height = scaledBottom - scaledTop;

      // Convert rotation to radians
      final radians = rotation * (3.14159265359 / 180.0);
      final cos = math.cos(radians);
      final sin = math.sin(radians);

      // Calculate rotated corners
      final halfWidth = width / 2;
      final halfHeight = height / 2;

      // Rotate around center
      final x1 = centerX + ((-halfWidth) * cos - (-halfHeight) * sin);
      final y1 = centerY + ((-halfWidth) * sin + (-halfHeight) * cos);
      final x2 = centerX + ((halfWidth) * cos - (-halfHeight) * sin);
      final y2 = centerY + ((halfWidth) * sin + (-halfHeight) * cos);
      final x3 = centerX + ((halfWidth) * cos - (halfHeight) * sin);
      final y3 = centerY + ((halfWidth) * sin + (halfHeight) * cos);
      final x4 = centerX + ((-halfWidth) * cos - (halfHeight) * sin);
      final y4 = centerY + ((-halfWidth) * sin + (halfHeight) * cos);

      // Find bounding box of rotated rectangle
      scaledLeft = math.min(math.min(x1, x2), math.min(x3, x4));
      scaledTop = math.min(math.min(y1, y2), math.min(y3, y4));
      scaledRight = math.max(math.max(x1, x2), math.max(x3, x4));
      scaledBottom = math.max(math.max(y1, y2), math.max(y3, y4));
    }

    // Apply offset
    return Rect.fromLTRB(
      scaledLeft + offsetX,
      scaledTop + offsetY,
      scaledRight + offsetX,
      scaledBottom + offsetY,
    );
  }
}

/// Device-specific coordinate calibration service
class DeviceCalibrationService {
  static const String _prefsKey = 'device_calibration_profiles';
  static DeviceCalibrationService? _instance;
  
  DeviceProfile? _currentProfile;
  final Map<String, DeviceProfile> _profiles = {};
  
  DeviceCalibrationService._();
  
  static DeviceCalibrationService get instance {
    _instance ??= DeviceCalibrationService._();
    return _instance!;
  }

  /// Initialize calibration service with current device
  Future<void> initialize(BuildContext context) async {
    await _loadProfiles();
    await _detectCurrentDevice(context);
  }

  /// Get current device profile
  DeviceProfile? get currentProfile => _currentProfile;

  /// Detect current device characteristics
  Future<void> _detectCurrentDevice(BuildContext context) async {
    final mediaQuery = MediaQuery.of(context);
    final deviceId = await _getDeviceId();
    final deviceModel = await _getDeviceModel();
    
    final profileKey = '${deviceId}_${mediaQuery.orientation.index}';
    
    if (_profiles.containsKey(profileKey)) {
      _currentProfile = _profiles[profileKey];
      print('📱 Loaded existing device profile: $deviceModel');
    } else {
      _currentProfile = DeviceProfile(
        deviceId: deviceId,
        deviceModel: deviceModel,
        screenSize: mediaQuery.size,
        pixelRatio: mediaQuery.devicePixelRatio,
        orientation: mediaQuery.orientation,
      );
      
      await _saveProfile(_currentProfile!);
      print('📱 Created new device profile: $deviceModel');
    }
  }

  /// Get device identifier
  Future<String> _getDeviceId() async {
    try {
      if (Platform.isAndroid) {
        return 'android_device';
      } else if (Platform.isIOS) {
        return 'ios_device';
      }
      return 'unknown_device';
    } catch (e) {
      return 'fallback_device';
    }
  }

  /// Get device model
  Future<String> _getDeviceModel() async {
    try {
      if (Platform.isAndroid) {
        return 'Android Device';
      } else if (Platform.isIOS) {
        return 'iOS Device';
      }
      return 'Unknown Device';
    } catch (e) {
      return 'Fallback Device';
    }
  }

  /// Calculate coordinate transformation
  CoordinateTransform calculateTransform(
    Size imageSize,
    Size screenSize, {
    DeviceProfile? profile,
  }) {
    profile ??= _currentProfile;
    
    if (profile == null || imageSize == Size.zero || screenSize == Size.zero) {
      return CoordinateTransform(
        scaleX: 1.0,
        scaleY: 1.0,
        offsetX: 0.0,
        offsetY: 0.0,
        rotation: 0.0,
        sourceSize: imageSize,
        targetSize: screenSize,
      );
    }

    // Calculate base transformation
    final imageAspectRatio = imageSize.width / imageSize.height;
    final screenAspectRatio = screenSize.width / screenSize.height;

    double scaleX, scaleY;
    double offsetX = 0, offsetY = 0;

    if (imageAspectRatio > screenAspectRatio) {
      // Image is wider than screen - fit to width
      scaleX = screenSize.width / imageSize.width;
      scaleY = scaleX;
      offsetY = (screenSize.height - (imageSize.height * scaleY)) / 2;
    } else {
      // Image is taller than screen - fit to height
      scaleY = screenSize.height / imageSize.height;
      scaleX = scaleY;
      offsetX = (screenSize.width - (imageSize.width * scaleX)) / 2;
    }

    // Apply device-specific calibration
    scaleX *= profile.scaleFactorX;
    scaleY *= profile.scaleFactorY;
    offsetX += profile.offsetX;
    offsetY += profile.offsetY;

    return CoordinateTransform(
      scaleX: scaleX,
      scaleY: scaleY,
      offsetX: offsetX,
      offsetY: offsetY,
      rotation: profile.rotation,
      sourceSize: imageSize,
      targetSize: screenSize,
    );
  }

  /// Update calibration parameters
  Future<void> updateCalibration({
    double? scaleFactorX,
    double? scaleFactorY,
    double? offsetX,
    double? offsetY,
    double? rotation,
  }) async {
    if (_currentProfile == null) return;

    _currentProfile = _currentProfile!.copyWith(
      scaleFactorX: scaleFactorX,
      scaleFactorY: scaleFactorY,
      offsetX: offsetX,
      offsetY: offsetY,
      rotation: rotation,
    );

    await _saveProfile(_currentProfile!);
    print('🔧 Updated calibration parameters');
  }

  /// Reset calibration to defaults
  Future<void> resetCalibration() async {
    await updateCalibration(
      scaleFactorX: 1.0,
      scaleFactorY: 1.0,
      offsetX: 0.0,
      offsetY: 0.0,
      rotation: -90.0, // Default -90° to align red box with screen
    );
  }

  /// Save device profile
  Future<void> _saveProfile(DeviceProfile profile) async {
    final profileKey = '${profile.deviceId}_${profile.orientation.index}';
    _profiles[profileKey] = profile;
    await _saveProfiles();
  }

  /// Load all profiles from storage
  Future<void> _loadProfiles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profilesJson = prefs.getString(_prefsKey);
      
      if (profilesJson != null) {
        final Map<String, dynamic> data = jsonDecode(profilesJson);
        _profiles.clear();
        
        data.forEach((key, value) {
          _profiles[key] = DeviceProfile.fromJson(value);
        });
        
        print('📱 Loaded ${_profiles.length} device profiles');
      }
    } catch (e) {
      print('❌ Error loading device profiles: $e');
    }
  }

  /// Save all profiles to storage
  Future<void> _saveProfiles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final Map<String, dynamic> data = {};
      
      _profiles.forEach((key, profile) {
        data[key] = profile.toJson();
      });
      
      await prefs.setString(_prefsKey, jsonEncode(data));
    } catch (e) {
      print('❌ Error saving device profiles: $e');
    }
  }

  /// Get calibration status
  bool get isCalibrated {
    return _currentProfile != null && 
           (_currentProfile!.scaleFactorX != 1.0 || 
            _currentProfile!.scaleFactorY != 1.0 ||
            _currentProfile!.offsetX != 0.0 || 
            _currentProfile!.offsetY != 0.0);
  }

  /// Get all saved profiles
  List<DeviceProfile> get allProfiles => _profiles.values.toList();
}
