// Simple MediaPipe Face Detector - Minimal implementation for comparison
import 'dart:io';
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:live_face_detection/models/recognition.dart';

/// Simple MediaPipe face detector for performance comparison
class SimpleMediaPipeDetector {
  FaceDetector? _faceDetector;
  bool _isInitialized = false;
  bool _isProcessing = false;
  int _lastInferenceTime = 0;
  
  /// Initialize MediaPipe detector
  Future<bool> initialize() async {
    try {
      print('🔄 Initializing Simple MediaPipe detector...');
      
      // Configure for speed and portrait orientation
      final options = FaceDetectorOptions(
        enableContours: false,
        enableLandmarks: false,
        enableClassification: false,
        enableTracking: false,
        minFaceSize: 0.15, // Slightly larger minimum for better portrait detection
        performanceMode: FaceDetectorMode.fast,
      );
      
      _faceDetector = FaceDetector(options: options);
      _isInitialized = true;
      
      print('✅ Simple MediaPipe detector initialized');
      return true;
    } catch (e) {
      print('❌ Failed to initialize MediaPipe: $e');
      return false;
    }
  }
  
  /// Detect faces (simplified)
  Future<int> detectFaceCount(CameraImage cameraImage) async {
    if (!_isInitialized || _faceDetector == null || _isProcessing) {
      return 0;
    }

    _isProcessing = true;

    try {
      // Convert CameraImage to InputImage (simplified)
      final inputImage = _convertToInputImage(cameraImage);
      if (inputImage == null) return 0;

      // Run detection
      final faces = await _faceDetector!.processImage(inputImage);

      return faces.length;
    } catch (e) {
      print('❌ MediaPipe detection error: $e');
      return 0;
    } finally {
      _isProcessing = false;
    }
  }

  /// Detect faces and return bounding boxes
  Future<List<Recognition>> detectFaces(CameraImage cameraImage) async {
    if (!_isInitialized || _faceDetector == null || _isProcessing) {
      return [];
    }

    _isProcessing = true;
    final stopwatch = Stopwatch()..start();

    try {
      // Convert CameraImage to InputImage
      final inputImage = _convertToInputImage(cameraImage);
      if (inputImage == null) return [];

      // Run detection
      final faces = await _faceDetector!.processImage(inputImage);

      stopwatch.stop();
      _lastInferenceTime = stopwatch.elapsedMilliseconds;

      // Convert to Recognition objects
      final recognitions = <Recognition>[];
      for (int i = 0; i < faces.length; i++) {
        final face = faces[i];
        final boundingBox = face.boundingBox;

        // Create Recognition object
        final recognition = Recognition(
          i,
          'Face',
          1.0, // MediaPipe doesn't provide confidence score, so we use 1.0
          Rect.fromLTWH(
            boundingBox.left.toDouble(),
            boundingBox.top.toDouble(),
            boundingBox.width.toDouble(),
            boundingBox.height.toDouble(),
          ),
        );

        recognitions.add(recognition);
      }

      return recognitions;
    } catch (e) {
      print('❌ MediaPipe detection error: $e');
      return [];
    } finally {
      _isProcessing = false;
    }
  }
  
  /// Convert CameraImage to InputImage with proper orientation handling
  InputImage? _convertToInputImage(CameraImage cameraImage) {
    try {
      // Use YUV format directly (more efficient)
      final WriteBuffer allBytes = WriteBuffer();
      for (final Plane plane in cameraImage.planes) {
        allBytes.putUint8List(plane.bytes);
      }
      final bytes = allBytes.done().buffer.asUint8List();

      // Determine proper rotation for portrait mode
      // Android cameras are typically landscape by default, need 90° rotation for portrait
      final rotation = _getInputImageRotation();

      return InputImage.fromBytes(
        bytes: bytes,
        metadata: InputImageMetadata(
          size: Size(cameraImage.width.toDouble(), cameraImage.height.toDouble()),
          rotation: rotation,
          format: InputImageFormat.yuv420,
          bytesPerRow: cameraImage.planes[0].bytesPerRow,
        ),
      );
    } catch (e) {
      print('❌ Error converting to InputImage: $e');
      return null;
    }
  }

  /// Get proper InputImageRotation for current device orientation
  InputImageRotation _getInputImageRotation() {
    // For Android devices in portrait mode, camera stream is typically landscape
    // and needs 90° rotation to match screen orientation
    if (Platform.isAndroid) {
      // Assume portrait mode (most common use case)
      return InputImageRotation.rotation90deg;
    } else {
      // iOS handles orientation differently
      return InputImageRotation.rotation0deg;
    }
  }
  
  /// Benchmark MediaPipe
  Future<Map<String, dynamic>> benchmark(CameraImage cameraImage, {int iterations = 10}) async {
    if (!_isInitialized) {
      return {'error': 'Not initialized'};
    }
    
    print('🏁 MediaPipe Benchmark - $iterations iterations');
    
    final times = <int>[];
    final detectionCounts = <int>[];
    
    for (int i = 0; i < iterations; i++) {
      final stopwatch = Stopwatch()..start();
      final count = await detectFaceCount(cameraImage);
      stopwatch.stop();
      
      times.add(stopwatch.elapsedMilliseconds);
      detectionCounts.add(count);
      
      await Future.delayed(const Duration(milliseconds: 50));
    }
    
    final avgTime = times.reduce((a, b) => a + b) / times.length;
    final avgDetections = detectionCounts.reduce((a, b) => a + b) / detectionCounts.length;
    
    print('📊 MediaPipe Results:');
    print('  Average Time: ${avgTime.round()}ms');
    print('  Average FPS: ${(1000 / avgTime).toStringAsFixed(1)}');
    print('  Average Detections: ${avgDetections.toStringAsFixed(1)}');
    
    return {
      'avgTime': avgTime.round(),
      'fps': (1000 / avgTime).toStringAsFixed(1),
      'avgDetections': avgDetections,
      'times': times,
      'detections': detectionCounts,
    };
  }
  
  /// Get performance metrics
  Map<String, dynamic> getPerformanceMetrics() {
    return {
      'name': 'MediaPipe',
      'initialized': _isInitialized,
      'processing': _isProcessing,
      'model': 'Google ML Kit Face Detection',
      'inferenceTime': _lastInferenceTime,
      'totalTime': _lastInferenceTime, // MediaPipe doesn't have separate total time
    };
  }

  /// Check if detector is ready
  bool get isReady => _isInitialized && !_isProcessing;

  /// Dispose
  void dispose() {
    _faceDetector?.close();
    _isInitialized = false;
  }
}
