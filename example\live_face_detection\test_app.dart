// Simple test to check if app compiles and basic functionality works
import 'package:flutter/material.dart';
import 'package:live_face_detection/service/simple_face_detector.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 Testing Live Face Detection App...');
  
  // Test 1: Check if SimpleFaceDetector can be created
  print('\n1. Testing SimpleFaceDetector creation...');
  try {
    final detector = SimpleFaceDetector();
    print('✅ SimpleFaceDetector created successfully');
    
    // Test 2: Try to initialize (will fail without assets, but should not crash)
    print('\n2. Testing initialization...');
    final success = await detector.initialize();
    if (success) {
      print('✅ Face detector initialized successfully');
    } else {
      print('⚠️ Face detector initialization failed (expected without assets)');
    }
    
    // Test 3: Check stats
    print('\n3. Testing stats...');
    final stats = detector.getStats();
    print('✅ Stats retrieved: $stats');
    
    // Test 4: Dispose
    print('\n4. Testing disposal...');
    detector.dispose();
    print('✅ Detector disposed successfully');
    
  } catch (e) {
    print('❌ Error testing SimpleFaceDetector: $e');
  }
  
  print('\n🎯 Test completed!');
  print('If no errors above, the app should compile successfully.');
}
