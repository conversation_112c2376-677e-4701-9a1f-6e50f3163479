// Isolate-based Face Detection Service
import 'dart:isolate';
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:live_face_detection/models/recognition.dart';

/// Face detection using background isolates for non-blocking performance
class IsolateFaceDetector {
  Isolate? _isolate;
  SendPort? _sendPort;
  ReceivePort? _receivePort;
  bool _isInitialized = false;
  
  /// Initialize background isolate for face detection
  Future<bool> initialize() async {
    try {
      print('🔄 Initializing isolate-based face detector...');
      
      // Create receive port for communication
      _receivePort = ReceivePort();
      
      // Spawn isolate
      _isolate = await Isolate.spawn(
        _isolateEntryPoint,
        _receivePort!.sendPort,
      );
      
      // Get send port from isolate
      final completer = Completer<SendPort>();
      _receivePort!.listen((message) {
        if (message is SendPort) {
          completer.complete(message);
        }
      });
      
      _sendPort = await completer.future;
      _isInitialized = true;
      
      print('✅ Isolate face detector initialized');
      return true;
    } catch (e) {
      print('❌ Failed to initialize isolate detector: $e');
      return false;
    }
  }
  
  /// Detect faces in background isolate
  Future<List<Recognition>> detectFaces(CameraImage cameraImage) async {
    if (!_isInitialized || _sendPort == null) {
      return [];
    }
    
    try {
      // Convert camera image to transferable format
      final imageData = _cameraImageToBytes(cameraImage);
      
      // Send to isolate for processing
      final responsePort = ReceivePort();
      _sendPort!.send({
        'command': 'detect',
        'imageData': imageData,
        'width': cameraImage.width,
        'height': cameraImage.height,
        'responsePort': responsePort.sendPort,
      });
      
      // Wait for result
      final result = await responsePort.first;
      responsePort.close();
      
      return _parseDetectionResults(result);
    } catch (e) {
      print('Error in isolate face detection: $e');
      return [];
    }
  }
  
  /// Convert camera image to bytes for isolate transfer
  Map<String, dynamic> _cameraImageToBytes(CameraImage cameraImage) {
    return {
      'yPlane': cameraImage.planes[0].bytes,
      'uPlane': cameraImage.planes[1].bytes,
      'vPlane': cameraImage.planes[2].bytes,
      'width': cameraImage.width,
      'height': cameraImage.height,
      'yRowStride': cameraImage.planes[0].bytesPerRow,
      'uvRowStride': cameraImage.planes[1].bytesPerRow,
      'uvPixelStride': cameraImage.planes[1].bytesPerPixel ?? 1,
    };
  }
  
  /// Parse detection results from isolate
  List<Recognition> _parseDetectionResults(dynamic result) {
    if (result is! List) return [];
    
    return result.map((item) {
      return Recognition(
        item['id'] as int,
        item['label'] as String,
        item['score'] as double,
        Rect.fromLTRB(
          item['x1'] as double,
          item['y1'] as double,
          item['x2'] as double,
          item['y2'] as double,
        ),
      );
    }).toList();
  }
  
  /// Dispose isolate
  void dispose() {
    _isolate?.kill();
    _receivePort?.close();
    _isInitialized = false;
  }
  
  /// Isolate entry point
  static void _isolateEntryPoint(SendPort mainSendPort) {
    final receivePort = ReceivePort();
    mainSendPort.send(receivePort.sendPort);
    
    // Initialize detector in isolate
    SimpleFaceDetector? detector;
    
    receivePort.listen((message) async {
      if (message is Map<String, dynamic>) {
        final command = message['command'] as String;
        final responsePort = message['responsePort'] as SendPort;
        
        switch (command) {
          case 'init':
            detector = SimpleFaceDetector();
            final success = await detector!.initialize();
            responsePort.send({'success': success});
            break;
            
          case 'detect':
            if (detector == null) {
              responsePort.send([]);
              return;
            }
            
            try {
              // Process image data in isolate
              final imageData = message['imageData'] as Map<String, dynamic>;
              final width = message['width'] as int;
              final height = message['height'] as int;
              
              // Convert bytes back to image format
              final cameraImage = _bytesToCameraImage(imageData);
              
              // Run detection
              final results = await detector!.detectFaces(cameraImage);
              
              // Convert results to transferable format
              final transferableResults = results.map((r) => {
                'id': r.id,
                'label': r.label,
                'score': r.score,
                'x1': r.location.left,
                'y1': r.location.top,
                'x2': r.location.right,
                'y2': r.location.bottom,
              }).toList();
              
              responsePort.send(transferableResults);
            } catch (e) {
              print('Error in isolate detection: $e');
              responsePort.send([]);
            }
            break;
        }
      }
    });
  }
  
  /// Convert bytes back to camera image format (simplified)
  static CameraImage _bytesToCameraImage(Map<String, dynamic> data) {
    // This is a simplified implementation
    // In practice, you'd need to reconstruct the full CameraImage
    throw UnimplementedError('Camera image reconstruction needed');
  }
}

/// Multi-threaded image processing utilities
class MultiThreadImageProcessor {
  /// Process image conversion across multiple threads
  static Future<Uint8List> convertYuvToRgbMultiThreaded(
    Uint8List yPlane,
    Uint8List uPlane,
    Uint8List vPlane,
    int width,
    int height,
  ) async {
    final numThreads = Platform.numberOfProcessors;
    final rowsPerThread = height ~/ numThreads;
    
    final futures = <Future<Uint8List>>[];
    
    for (int i = 0; i < numThreads; i++) {
      final startRow = i * rowsPerThread;
      final endRow = (i == numThreads - 1) ? height : (i + 1) * rowsPerThread;
      
      futures.add(
        compute(_convertYuvChunk, {
          'yPlane': yPlane,
          'uPlane': uPlane,
          'vPlane': vPlane,
          'width': width,
          'startRow': startRow,
          'endRow': endRow,
        })
      );
    }
    
    final results = await Future.wait(futures);
    
    // Combine results
    final totalBytes = width * height * 4; // RGBA
    final combined = Uint8List(totalBytes);
    int offset = 0;
    
    for (final chunk in results) {
      combined.setRange(offset, offset + chunk.length, chunk);
      offset += chunk.length;
    }
    
    return combined;
  }
  
  /// Convert YUV chunk in separate thread
  static Uint8List _convertYuvChunk(Map<String, dynamic> params) {
    final yPlane = params['yPlane'] as Uint8List;
    final uPlane = params['uPlane'] as Uint8List;
    final vPlane = params['vPlane'] as Uint8List;
    final width = params['width'] as int;
    final startRow = params['startRow'] as int;
    final endRow = params['endRow'] as int;
    
    final chunkHeight = endRow - startRow;
    final rgbaChunk = Uint8List(width * chunkHeight * 4);
    
    // Process YUV to RGB for this chunk
    for (int y = startRow; y < endRow; y++) {
      for (int x = 0; x < width; x++) {
        final yIndex = y * width + x;
        final uvIndex = (y ~/ 2) * (width ~/ 2) + (x ~/ 2);
        
        if (yIndex < yPlane.length && uvIndex < uPlane.length && uvIndex < vPlane.length) {
          final yValue = yPlane[yIndex];
          final uValue = uPlane[uvIndex] - 128;
          final vValue = vPlane[uvIndex] - 128;
          
          final r = (yValue + 1.402 * vValue).clamp(0, 255).toInt();
          final g = (yValue - 0.344 * uValue - 0.714 * vValue).clamp(0, 255).toInt();
          final b = (yValue + 1.772 * uValue).clamp(0, 255).toInt();
          
          final pixelIndex = ((y - startRow) * width + x) * 4;
          rgbaChunk[pixelIndex] = r;
          rgbaChunk[pixelIndex + 1] = g;
          rgbaChunk[pixelIndex + 2] = b;
          rgbaChunk[pixelIndex + 3] = 255;
        }
      }
    }
    
    return rgbaChunk;
  }
}
