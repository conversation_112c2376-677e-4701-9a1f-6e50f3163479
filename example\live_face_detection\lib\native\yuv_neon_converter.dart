// Native ARM NEON YUV Converter for RK3399 optimization
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:camera/camera.dart';
import 'package:image/image.dart' as img;

/// Native ARM NEON optimized YUV to RGB converter
/// Specifically optimized for RK3399 chip on Telpo F8
class YuvNeonConverter {
  static const MethodChannel _channel = MethodChannel('yuv_neon_converter');
  
  /// Convert YUV420 camera image to RGB using ARM NEON SIMD
  /// Expected 5-10x performance improvement on RK3399
  static Future<img.Image?> convertYuv420ToRgbNeon(CameraImage cameraImage) async {
    try {
      final width = cameraImage.width;
      final height = cameraImage.height;
      
      // Extract YUV planes
      final yPlane = cameraImage.planes[0];
      final uPlane = cameraImage.planes[1];
      final vPlane = cameraImage.planes[2];
      
      // Prepare output buffer for RGB data
      final rgbSize = width * height * 3; // RGB = 3 bytes per pixel
      final rgbOutput = Uint8List(rgbSize);
      
      // Call native ARM NEON conversion
      final stopwatch = Stopwatch()..start();
      
      await _channel.invokeMethod('convertYuv420ToRgb', {
        'yPlane': yPlane.bytes,
        'yStride': yPlane.bytesPerRow,
        'uPlane': uPlane.bytes,
        'uStride': uPlane.bytesPerRow,
        'vPlane': vPlane.bytes,
        'vStride': vPlane.bytesPerRow,
        'rgbOutput': rgbOutput,
        'rgbStride': width * 3,
        'width': width,
        'height': height,
      });
      
      stopwatch.stop();
      print('🚀 NEON YUV conversion: ${stopwatch.elapsedMilliseconds}ms');
      
      // Create image from RGB data
      final image = img.Image.fromBytes(
        width: width,
        height: height,
        bytes: rgbOutput.buffer,
        format: img.Format.uint8,
        numChannels: 3,
      );
      
      return image;
    } catch (e) {
      print('❌ NEON YUV conversion failed: $e');
      return null;
    }
  }
  
  /// Fallback Dart YUV conversion for comparison
  static img.Image convertYuv420ToRgbDart(CameraImage cameraImage) {
    final stopwatch = Stopwatch()..start();
    
    final width = cameraImage.width;
    final height = cameraImage.height;
    
    final yPlane = cameraImage.planes[0].bytes;
    final uPlane = cameraImage.planes[1].bytes;
    final vPlane = cameraImage.planes[2].bytes;
    
    final image = img.Image(width: width, height: height);
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final yIndex = y * width + x;
        final uvIndex = (y ~/ 2) * (width ~/ 2) + (x ~/ 2);
        
        if (yIndex < yPlane.length && uvIndex < uPlane.length && uvIndex < vPlane.length) {
          final yValue = yPlane[yIndex];
          final uValue = uPlane[uvIndex] - 128;
          final vValue = vPlane[uvIndex] - 128;
          
          final r = (yValue + 1.402 * vValue).clamp(0, 255).toInt();
          final g = (yValue - 0.344 * uValue - 0.714 * vValue).clamp(0, 255).toInt();
          final b = (yValue + 1.772 * uValue).clamp(0, 255).toInt();
          
          image.setPixelRgb(x, y, r, g, b);
        }
      }
    }
    
    stopwatch.stop();
    print('🐌 Dart YUV conversion: ${stopwatch.elapsedMilliseconds}ms');
    
    return image;
  }
  
  /// Benchmark NEON vs Dart conversion
  static Future<void> benchmarkConversion(CameraImage cameraImage) async {
    print('\n🏁 YUV Conversion Benchmark - RK3399 ARM NEON');
    print('Image size: ${cameraImage.width}x${cameraImage.height}');
    
    // Test NEON conversion
    final neonStopwatch = Stopwatch()..start();
    final neonResult = await convertYuv420ToRgbNeon(cameraImage);
    neonStopwatch.stop();
    
    // Test Dart conversion
    final dartStopwatch = Stopwatch()..start();
    final dartResult = convertYuv420ToRgbDart(cameraImage);
    dartStopwatch.stop();
    
    final neonTime = neonStopwatch.elapsedMilliseconds;
    final dartTime = dartStopwatch.elapsedMilliseconds;
    final speedup = dartTime / neonTime;
    
    print('📊 Benchmark Results:');
    print('  NEON conversion: ${neonTime}ms');
    print('  Dart conversion: ${dartTime}ms');
    print('  Speedup: ${speedup.toStringAsFixed(1)}x faster');
    print('  Performance gain: ${((speedup - 1) * 100).toStringAsFixed(0)}%');
    
    if (neonResult != null) {
      print('✅ NEON conversion successful');
    } else {
      print('❌ NEON conversion failed');
    }
  }
  
  /// Check if ARM NEON is available on device
  static Future<bool> isNeonAvailable() async {
    try {
      final result = await _channel.invokeMethod('isNeonAvailable');
      return result as bool;
    } catch (e) {
      print('Failed to check NEON availability: $e');
      return false;
    }
  }
  
  /// Get device CPU information
  static Future<Map<String, dynamic>> getCpuInfo() async {
    try {
      final result = await _channel.invokeMethod('getCpuInfo');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      print('Failed to get CPU info: $e');
      return {};
    }
  }
}

/// Performance comparison utility
class YuvConversionBenchmark {
  static final List<int> _neonTimes = [];
  static final List<int> _dartTimes = [];
  
  static void recordNeonTime(int timeMs) {
    _neonTimes.add(timeMs);
    if (_neonTimes.length > 50) _neonTimes.removeAt(0);
  }
  
  static void recordDartTime(int timeMs) {
    _dartTimes.add(timeMs);
    if (_dartTimes.length > 50) _dartTimes.removeAt(0);
  }
  
  static Map<String, dynamic> getStats() {
    if (_neonTimes.isEmpty || _dartTimes.isEmpty) {
      return {'error': 'No data available'};
    }
    
    final avgNeon = _neonTimes.reduce((a, b) => a + b) / _neonTimes.length;
    final avgDart = _dartTimes.reduce((a, b) => a + b) / _dartTimes.length;
    final speedup = avgDart / avgNeon;
    
    return {
      'avgNeonTime': avgNeon.round(),
      'avgDartTime': avgDart.round(),
      'speedup': speedup,
      'performanceGain': ((speedup - 1) * 100).round(),
      'sampleCount': _neonTimes.length,
    };
  }
  
  static void printStats() {
    final stats = getStats();
    if (stats.containsKey('error')) {
      print('No benchmark data available');
      return;
    }
    
    print('\n📈 YUV Conversion Performance Stats:');
    print('  Average NEON time: ${stats['avgNeonTime']}ms');
    print('  Average Dart time: ${stats['avgDartTime']}ms');
    print('  Average speedup: ${stats['speedup'].toStringAsFixed(1)}x');
    print('  Performance gain: ${stats['performanceGain']}%');
    print('  Sample count: ${stats['sampleCount']}');
  }
}
