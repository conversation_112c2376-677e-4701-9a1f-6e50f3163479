# Hướng dẫn Build APK cho Object Detection SSD MobileNet V2

## ✅ Đã nâng cấp Gradle theo hướng dẫn chính thức Flutter!

Dự án này đã được cập nhật để sử dụng Flutter's Plugin DSL (declarative syntax) thay vì imperative apply method cũ.

## Yêu cầu hệ thống
- Flutter SDK đã được cài đặt và cấu hình
- Android SDK và Android Studio
- Java Development Kit (JDK) 17 hoặc cao hơn

## Các bước thực hiện

### Bước 1: Mở Command Prompt hoặc PowerShell
Mở Command Prompt hoặc PowerShell với quyền Administrator

### Bước 2: <PERSON><PERSON><PERSON><PERSON> hướng đến thư mục dự án
```cmd
cd "c:\Users\<USER>\workspace\flutter-tflite\example\object_detection_ssd_mobilenet_v2"
```

### Bước 3: <PERSON><PERSON><PERSON> tra Flutter
```cmd
flutter doctor
```
<PERSON><PERSON><PERSON> bảo tất cả các thành phần cần thiết đều được cài đặt đúng.

### Bước 4: Lấy dependencies
```cmd
flutter pub get
```

### Bước 5: Kiểm tra các thiết bị có sẵn
```cmd
flutter devices
```

### Bước 6: Build APK
```cmd
flutter build apk --release
```

Hoặc nếu muốn build APK cho nhiều kiến trúc:
```cmd
flutter build apk --split-per-abi
```

### Bước 7: Tìm file APK
Sau khi build thành công, file APK sẽ được tạo tại:
```
build\app\outputs\flutter-apk\app-release.apk
```

Hoặc nếu build với --split-per-abi:
```
build\app\outputs\flutter-apk\app-arm64-v8a-release.apk
build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk
build\app\outputs\flutter-apk\app-x86_64-release.apk
```

## Xử lý sự cố

### Nếu gặp lỗi về dependencies:
```cmd
flutter clean
flutter pub get
```

### Nếu gặp lỗi về Android SDK:
```cmd
flutter doctor --android-licenses
```

### Nếu gặp lỗi về Gradle:
```cmd
cd android
.\gradlew clean
cd ..
flutter build apk --release
```

## Lưu ý
- File APK được tạo sẽ có kích thước khoảng 20-50MB tùy thuộc vào model TensorFlow Lite
- APK này chứa model AI để nhận diện đối tượng sử dụng SSD MobileNet V2
- Để cài đặt APK trên thiết bị Android, cần bật "Unknown sources" trong Settings

## Kiểm tra APK
Sau khi build thành công, bạn có thể:
1. Cài đặt APK trên thiết bị Android
2. Kiểm tra kích thước file APK
3. Test ứng dụng với camera hoặc ảnh từ thư viện
