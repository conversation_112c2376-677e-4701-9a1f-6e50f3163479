// Unified Face Detection Service
// Supports multiple models: <PERSON>Face, UltraFace RFB, UltraFace Slim
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:live_face_detection/models/recognition.dart';
import 'package:live_face_detection/models/face_detection_model.dart';
import 'package:live_face_detection/service/simple_face_detector.dart';
import 'package:live_face_detection/service/ultraface_detector.dart';
import 'package:live_face_detection/service/simple_mediapipe_detector.dart';

/// Unified Face Detection Service
/// Manages multiple face detection models and provides a unified interface
class UnifiedFaceDetector {
  // Model instances
  SimpleFaceDetector? _blazeFaceDetector;
  UltraFaceDetector? _ultraFaceRFBDetector;
  UltraFaceDetector? _ultraFaceSlimDetector;
  UltraFaceDetector? _ultraFaceSlimINT8Detector;
  SimpleMediaPipeDetector? _mediaPipeDetector;
  
  // Current active model
  FaceDetectionModel _currentModel = FaceDetectionModel.blazeFace;
  bool _isInitialized = false;
  
  // Performance tracking
  final Map<FaceDetectionModel, List<int>> _performanceHistory = {};
  
  /// Initialize all face detection models
  Future<bool> initializeAllModels() async {
    try {
      print('🔄 Initializing all face detection models...');
      print('🔍 DEBUG: Default model set to ${_currentModel.displayName}');
      
      // Initialize BlazeFace
      _blazeFaceDetector = SimpleFaceDetector();
      final blazeFaceSuccess = await _blazeFaceDetector!.initialize();
      
      // Initialize UltraFace RFB
      _ultraFaceRFBDetector = UltraFaceDetector();
      final ultraFaceRFBSuccess = await _ultraFaceRFBDetector!.initialize(FaceDetectionModel.ultraFaceRFB);
      
      // Initialize UltraFace Slim
      _ultraFaceSlimDetector = UltraFaceDetector();
      final ultraFaceSlimSuccess = await _ultraFaceSlimDetector!.initialize(FaceDetectionModel.ultraFaceSlim);

      // Initialize UltraFace Slim INT8 (Quantized)
      _ultraFaceSlimINT8Detector = UltraFaceDetector();
      final ultraFaceSlimINT8Success = await _ultraFaceSlimINT8Detector!.initialize(FaceDetectionModel.ultraFaceSlimINT8);

      // Initialize MediaPipe
      _mediaPipeDetector = SimpleMediaPipeDetector();
      final mediaPipeSuccess = await _mediaPipeDetector!.initialize();

      // Initialize performance tracking
      for (final model in FaceDetectionModel.values) {
        _performanceHistory[model] = [];
      }

      _isInitialized = blazeFaceSuccess && ultraFaceRFBSuccess && ultraFaceSlimSuccess && ultraFaceSlimINT8Success && mediaPipeSuccess;
      
      if (_isInitialized) {
        print('✅ All face detection models initialized successfully');
        print('📊 Available models:');
        for (final model in FaceDetectionModel.values) {
          final specs = ModelSpecs.getSpecs(model);
          print('  - ${model.displayName}: ${specs.inputWidth}x${specs.inputHeight}, threshold: ${specs.defaultConfidenceThreshold}');
        }
      } else {
        print('❌ Some models failed to initialize');
      }
      
      return _isInitialized;
    } catch (e) {
      print('❌ Error initializing models: $e');
      return false;
    }
  }
  
  /// Switch to a different model
  Future<bool> switchModel(FaceDetectionModel newModel) async {
    if (!_isInitialized) {
      print('❌ Models not initialized');
      return false;
    }

    if (_currentModel == newModel) {
      print('ℹ️ Already using ${newModel.displayName}');
      return true;
    }

    print('🔄 Switching from ${_currentModel.displayName} to ${newModel.displayName}');
    _currentModel = newModel;

    // Log model switch
    final specs = ModelSpecs.getSpecs(newModel);
    print('📋 Model specs: ${specs.inputWidth}x${specs.inputHeight}, threshold: ${specs.defaultConfidenceThreshold}');
    print('📝 Description: ${specs.description}');

    return true;
  }

  /// DEBUG: Force set model to BlazeFace for debugging
  void forceBlazeFace() {
    print('🔧 DEBUG: Force setting model to BlazeFace');
    _currentModel = FaceDetectionModel.blazeFace;
    print('🔍 DEBUG: Current model is now ${_currentModel.displayName}');
  }
  
  /// Detect faces using the current active model
  Future<List<Recognition>> detectFaces(CameraImage cameraImage) async {
    if (!_isInitialized) return [];

    // Debug: Log which model is being used
    print('🔍 DEBUG: Using model ${_currentModel.displayName}');

    final stopwatch = Stopwatch()..start();
    List<Recognition> results = [];

    try {
      switch (_currentModel) {
        case FaceDetectionModel.blazeFace:
          if (_blazeFaceDetector != null) {
            print('🔍 DEBUG: Calling BlazeFace detector');
            results = await _blazeFaceDetector!.detectFaces(cameraImage);
            print('🔍 DEBUG: BlazeFace returned ${results.length} faces');
          }
          break;

        case FaceDetectionModel.ultraFaceRFB:
          if (_ultraFaceRFBDetector != null) {
            print('🔍 DEBUG: Calling UltraFace RFB detector');
            results = await _ultraFaceRFBDetector!.detectFaces(cameraImage);
            print('🔍 DEBUG: UltraFace RFB returned ${results.length} faces');
          }
          break;

        case FaceDetectionModel.ultraFaceSlim:
          if (_ultraFaceSlimDetector != null) {
            print('🔍 DEBUG: Calling UltraFace Slim detector');
            results = await _ultraFaceSlimDetector!.detectFaces(cameraImage);
            print('🔍 DEBUG: UltraFace Slim returned ${results.length} faces');
          }
          break;

        case FaceDetectionModel.ultraFaceSlimINT8:
          if (_ultraFaceSlimINT8Detector != null) {
            print('🔍 DEBUG: Calling UltraFace Slim INT8 detector');
            results = await _ultraFaceSlimINT8Detector!.detectFaces(cameraImage);
            print('🔍 DEBUG: UltraFace Slim INT8 returned ${results.length} faces');
          }
          break;

        case FaceDetectionModel.mediaPipe:
          if (_mediaPipeDetector != null) {
            print('🔍 DEBUG: Calling MediaPipe detector');
            // MediaPipe returns proper Recognition objects with bounding boxes
            results = await _mediaPipeDetector!.detectFaces(cameraImage);
            print('🔍 DEBUG: MediaPipe returned ${results.length} faces');
          }
          break;
      }

      stopwatch.stop();

      // Track performance
      _trackPerformance(_currentModel, stopwatch.elapsedMilliseconds);

      return results;
    } catch (e) {
      print('Error in unified face detection: $e');
      return [];
    }
  }
  
  /// Track performance metrics for each model
  void _trackPerformance(FaceDetectionModel model, int inferenceTime) {
    final history = _performanceHistory[model] ?? [];
    history.add(inferenceTime);
    
    // Keep only last 100 measurements
    if (history.length > 100) {
      history.removeAt(0);
    }
    
    _performanceHistory[model] = history;
  }
  
  /// Get performance metrics for current model
  Map<String, dynamic> getCurrentModelMetrics() {
    switch (_currentModel) {
      case FaceDetectionModel.blazeFace:
        return _blazeFaceDetector?.getPerformanceMetrics() ?? {};
        
      case FaceDetectionModel.ultraFaceRFB:
        return _ultraFaceRFBDetector?.getPerformanceMetrics() ?? {};
        
      case FaceDetectionModel.ultraFaceSlim:
        return _ultraFaceSlimDetector?.getPerformanceMetrics() ?? {};

      case FaceDetectionModel.ultraFaceSlimINT8:
        return _ultraFaceSlimINT8Detector?.getPerformanceMetrics() ?? {};
      case FaceDetectionModel.mediaPipe:
        return _mediaPipeDetector?.getPerformanceMetrics() ?? {
          'name': 'MediaPipe',
          'initialized': _mediaPipeDetector != null,
          'processing': false,
        };
    }
  }
  
  /// Get performance comparison across all models
  Map<String, Map<String, dynamic>> getPerformanceComparison() {
    final comparison = <String, Map<String, dynamic>>{};
    
    for (final model in FaceDetectionModel.values) {
      final history = _performanceHistory[model] ?? [];
      if (history.isNotEmpty) {
        final avgTime = history.reduce((a, b) => a + b) / history.length;
        final minTime = history.reduce((a, b) => a < b ? a : b);
        final maxTime = history.reduce((a, b) => a > b ? a : b);
        final specs = ModelSpecs.getSpecs(model);
        
        comparison[model.displayName] = {
          'averageTime': avgTime.round(),
          'minTime': minTime,
          'maxTime': maxTime,
          'sampleCount': history.length,
          'inputSize': '${specs.inputWidth}x${specs.inputHeight}',
          'threshold': specs.defaultConfidenceThreshold,
          'description': specs.description,
        };
      }
    }
    
    return comparison;
  }
  
  /// Update confidence threshold for current model
  void updateConfidenceThreshold(double threshold) {
    switch (_currentModel) {
      case FaceDetectionModel.blazeFace:
        // BlazeFace doesn't have updateConfidenceThreshold method
        // Would need to add it to SimpleFaceDetector
        break;
        
      case FaceDetectionModel.ultraFaceRFB:
        _ultraFaceRFBDetector?.updateConfidenceThreshold(threshold);
        break;
        
      case FaceDetectionModel.ultraFaceSlim:
        _ultraFaceSlimDetector?.updateConfidenceThreshold(threshold);
        break;

      case FaceDetectionModel.ultraFaceSlimINT8:
        _ultraFaceSlimINT8Detector?.updateConfidenceThreshold(threshold);
        break;

      case FaceDetectionModel.mediaPipe:
        // MediaPipe confidence threshold is handled internally
        break;
    }
  }
  
  /// Get list of available models
  List<FaceDetectionModel> getAvailableModels() {
    return FaceDetectionModel.values;
  }
  
  /// Get current active model
  FaceDetectionModel get currentModel => _currentModel;
  
  /// Check if all models are ready
  bool get isReady {
    return _isInitialized &&
           (_blazeFaceDetector?.isReady ?? false) &&
           (_ultraFaceRFBDetector?.isReady ?? false) &&
           (_ultraFaceSlimDetector?.isReady ?? false) &&
           (_ultraFaceSlimINT8Detector?.isReady ?? false) &&
           (_mediaPipeDetector?.isReady ?? false);
  }
  
  /// Get model specifications
  ModelSpecs getCurrentModelSpecs() {
    return ModelSpecs.getSpecs(_currentModel);
  }
  
  /// Get detailed model info
  Map<String, dynamic> getModelInfo() {
    final specs = getCurrentModelSpecs();
    final metrics = getCurrentModelMetrics();
    
    return {
      'name': _currentModel.displayName,
      'inputSize': '${specs.inputWidth}x${specs.inputHeight}',
      'channels': specs.channels,
      'threshold': specs.defaultConfidenceThreshold,
      'description': specs.description,
      'performance': metrics,
    };
  }
  
  /// Dispose all resources
  void dispose() {
    _blazeFaceDetector?.dispose();
    _ultraFaceRFBDetector?.dispose();
    _ultraFaceSlimDetector?.dispose();
    
    _blazeFaceDetector = null;
    _ultraFaceRFBDetector = null;
    _ultraFaceSlimDetector = null;
    
    _isInitialized = false;
    _performanceHistory.clear();
    
    print('🗑️ All face detection models disposed');
  }
}
