import 'dart:async';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:live_face_detection/models/recognition.dart';
import 'package:live_face_detection/service/simple_mediapipe_detector.dart';

/// Simple Camera Widget with MediaPipe Face Detection and Bounding Box Display
class SimpleMediaPipeCameraWidget extends StatefulWidget {
  const SimpleMediaPipeCameraWidget({super.key});

  @override
  State<SimpleMediaPipeCameraWidget> createState() => _SimpleMediaPipeCameraWidgetState();
}

class _SimpleMediaPipeCameraWidgetState extends State<SimpleMediaPipeCameraWidget> {
  CameraController? _cameraController;
  SimpleMediaPipeDetector? _detector;
  bool _isInitialized = false;
  bool _isDetecting = false;
  
  List<Recognition> _recognitions = [];
  Size _imageSize = Size.zero;
  
  // Performance tracking
  int _inferenceTime = 0;
  int _totalTime = 0;
  double _fps = 0.0;
  
  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    _detector?.dispose();
    super.dispose();
  }

  Future<void> _initializeCamera() async {
    try {
      // Get available cameras
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        print('❌ No cameras available');
        return;
      }

      // Initialize camera controller
      _cameraController = CameraController(
        cameras.first,
        ResolutionPreset.medium,
        enableAudio: false,
      );

      await _cameraController!.initialize();

      // Initialize detector
      _detector = SimpleMediaPipeDetector();
      final initialized = await _detector!.initialize();
      
      if (!initialized) {
        print('❌ Failed to initialize detector');
        return;
      }

      // Start image stream
      _cameraController!.startImageStream(_processCameraImage);
      
      setState(() {
        _isInitialized = true;
        _imageSize = Size(
          _cameraController!.value.previewSize!.height,
          _cameraController!.value.previewSize!.width,
        );
      });

      print('✅ Camera and detector initialized successfully');
    } catch (e) {
      print('❌ Error initializing camera: $e');
    }
  }

  Future<void> _processCameraImage(CameraImage cameraImage) async {
    if (_isDetecting || _detector == null) return;
    
    _isDetecting = true;
    final totalStopwatch = Stopwatch()..start();
    
    try {
      // Detect faces with bounding boxes
      final inferenceStopwatch = Stopwatch()..start();
      final recognitions = await _detector!.detectFaces(cameraImage);
      inferenceStopwatch.stop();
      
      totalStopwatch.stop();
      
      // Update performance metrics
      _inferenceTime = inferenceStopwatch.elapsedMilliseconds;
      _totalTime = totalStopwatch.elapsedMilliseconds;
      _fps = _totalTime > 0 ? 1000 / _totalTime : 0;
      
      // Update UI
      if (mounted) {
        setState(() {
          _recognitions = recognitions;
        });
      }
      
      // Debug logging
      if (recognitions.isNotEmpty) {
        print('🎯 Detected ${recognitions.length} faces in ${_inferenceTime}ms');
      }
      
    } catch (e) {
      print('❌ Error processing camera image: $e');
    } finally {
      _isDetecting = false;
    }
  }

  Widget _buildLoadingScreen() {
    return const Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.green),
            SizedBox(height: 16),
            Text(
              'Initializing Camera & MediaPipe...',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceOverlay() {
    return Positioned(
      top: 50,
      left: 16,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'MediaPipe Face Detection',
              style: const TextStyle(
                color: Colors.green,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Faces: ${_recognitions.length}',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
            Text(
              'Inference: ${_inferenceTime}ms',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
            Text(
              'Total: ${_totalTime}ms',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
            Text(
              'FPS: ${_fps.toStringAsFixed(1)}',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized || _cameraController == null) {
      return _buildLoadingScreen();
    }

    if (!_cameraController!.value.isInitialized) {
      return _buildLoadingScreen();
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera Preview
          Positioned.fill(
            child: AspectRatio(
              aspectRatio: _cameraController!.value.aspectRatio,
              child: CameraPreview(_cameraController!),
            ),
          ),

          // Face Detection Overlay
          Positioned.fill(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return CustomPaint(
                  painter: FaceDetectionPainter(
                    recognitions: _recognitions,
                    imageSize: _imageSize,
                    previewSize: Size(constraints.maxWidth, constraints.maxHeight),
                    cameraController: _cameraController!,
                    flipHorizontally: false, // Simple widget uses auto-detection
                    flipVertically: false, // Simple widget uses auto-detection
                  ),
                );
              },
            ),
          ),

          // Performance Overlay
          _buildPerformanceOverlay(),
        ],
      ),
    );
  }
}

/// Custom painter for drawing face detection bounding boxes
class FaceDetectionPainter extends CustomPainter {
  final List<Recognition> recognitions;
  final Size imageSize;
  final Size previewSize;
  final CameraController cameraController;
  final bool flipHorizontally;
  final bool flipVertically;

  FaceDetectionPainter({
    required this.recognitions,
    required this.imageSize,
    required this.previewSize,
    required this.cameraController,
    required this.flipHorizontally,
    required this.flipVertically,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (recognitions.isEmpty || imageSize == Size.zero) return;

    final paint = Paint()
      ..color = Colors.green
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    final textPaint = Paint()..color = Colors.green;

    for (final recognition in recognitions) {
      final rect = _scaleRect(recognition.location);
      
      // Draw bounding box
      canvas.drawRect(rect, paint);
      
      // Draw label and confidence
      final textSpan = TextSpan(
        text: '${recognition.label} ${(recognition.score * 100).toStringAsFixed(0)}%',
        style: const TextStyle(
          color: Colors.green,
          fontSize: 14,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(
              offset: Offset(1, 1),
              blurRadius: 2,
              color: Colors.black,
            ),
          ],
        ),
      );
      
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(rect.left, rect.top - 25),
      );
    }
  }

  Rect _scaleRect(Rect rect) {
    if (imageSize == Size.zero || previewSize == Size.zero) {
      return rect;
    }

    // For simple MediaPipe widget, the overlay is positioned over the full screen
    // but the camera preview is in an AspectRatio widget. We need to calculate
    // the actual preview area within the screen bounds.

    // Get camera aspect ratio
    final cameraAspectRatio = cameraController.value.aspectRatio;
    final previewAspectRatio = previewSize.width / previewSize.height;

    // Calculate the actual preview area within the widget bounds
    Size actualPreviewSize;
    Offset previewOffset = Offset.zero;

    if (cameraAspectRatio > previewAspectRatio) {
      // Camera is wider than preview area - fit by height
      actualPreviewSize = Size(
        previewSize.height * cameraAspectRatio,
        previewSize.height,
      );
      previewOffset = Offset(
        (previewSize.width - actualPreviewSize.width) / 2,
        0,
      );
    } else {
      // Camera is taller than preview area - fit by width
      actualPreviewSize = Size(
        previewSize.width,
        previewSize.width / cameraAspectRatio,
      );
      previewOffset = Offset(
        0,
        (previewSize.height - actualPreviewSize.height) / 2,
      );
    }

    // Scale coordinates from image space to preview space
    final scaleX = actualPreviewSize.width / imageSize.width;
    final scaleY = actualPreviewSize.height / imageSize.height;

    // Check if we need to flip coordinates for Telpo F8 or front-facing cameras
    final needsHorizontalFlip = _shouldFlipHorizontally();
    final needsVerticalFlip = _shouldFlipVertically();

    double left, right;
    if (needsHorizontalFlip) {
      // Flip horizontally for mirrored cameras (Telpo F8)
      left = (imageSize.width - rect.right) * scaleX;
      right = (imageSize.width - rect.left) * scaleX;
    } else {
      left = rect.left * scaleX;
      right = rect.right * scaleX;
    }

    double top, bottom;
    if (needsVerticalFlip) {
      // Flip vertically for Telpo F8 (both cameras on front side)
      top = (imageSize.height - rect.bottom) * scaleY;
      bottom = (imageSize.height - rect.top) * scaleY;
    } else {
      top = rect.top * scaleY;
      bottom = rect.bottom * scaleY;
    }

    // Apply scaling and offset
    return Rect.fromLTRB(
      left + previewOffset.dx,
      top + previewOffset.dy,
      right + previewOffset.dx,
      bottom + previewOffset.dy,
    );
  }

  /// Check if we should flip coordinates horizontally
  /// Returns true for Telpo F8 or front-facing cameras that show mirrored image
  bool _shouldFlipHorizontally() {
    // Use manual toggle if provided
    if (flipHorizontally) {
      return true;
    }

    // Auto-detect front-facing cameras (usually mirrored)
    final cameraDescription = cameraController.description;
    if (cameraDescription.lensDirection == CameraLensDirection.front) {
      return true;
    }

    return false; // Default: no flip for normal back cameras
  }

  /// Check if we should flip coordinates vertically
  /// Returns true for Telpo F8 where both cameras are on front side
  bool _shouldFlipVertically() {
    // For simple MediaPipe widget, use auto-detection
    // You can add device-specific detection here if needed

    return false; // Default: no vertical flip for normal cameras
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
