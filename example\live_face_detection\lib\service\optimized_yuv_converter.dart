// Optimized YUV Converter - Pure Dart with RK3399 optimizations
import 'dart:typed_data';
import 'dart:isolate';
import 'package:camera/camera.dart';
import 'package:image/image.dart' as img;
import 'package:flutter/foundation.dart';

/// Highly optimized YUV to RGB converter for RK3399
/// Uses pure Dart optimizations instead of native code
class OptimizedYuvConverter {
  
  /// Convert YUV420 to RGB using optimized Dart implementation
  /// Specifically tuned for RK3399 performance characteristics
  static img.Image convertYuv420ToRgbOptimized(CameraImage cameraImage) {
    final stopwatch = Stopwatch()..start();
    
    final width = cameraImage.width;
    final height = cameraImage.height;
    
    // Pre-allocate output image for better memory performance
    final image = img.Image(width: width, height: height);
    
    // Get plane data with bounds checking
    final yPlane = cameraImage.planes[0].bytes;
    final uPlane = cameraImage.planes[1].bytes;
    final vPlane = cameraImage.planes[2].bytes;
    
    final yStride = cameraImage.planes[0].bytesPerRow;
    final uStride = cameraImage.planes[1].bytesPerRow;
    final vStride = cameraImage.planes[2].bytesPerRow;
    
    // Optimized conversion with integer arithmetic
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final yIndex = y * yStride + x;
        final uvIndex = (y ~/ 2) * uStride + (x ~/ 2);
        
        // Bounds checking
        if (yIndex >= yPlane.length || uvIndex >= uPlane.length || uvIndex >= vPlane.length) {
          continue;
        }
        
        // Get YUV values
        final yValue = yPlane[yIndex];
        final uValue = uPlane[uvIndex] - 128;
        final vValue = vPlane[uvIndex] - 128;
        
        // Optimized YUV to RGB conversion using integer math
        // Coefficients scaled by 1024 for integer arithmetic
        final r = (yValue + ((1402 * vValue) >> 10)).clamp(0, 255);
        final g = (yValue - ((344 * uValue + 714 * vValue) >> 10)).clamp(0, 255);
        final b = (yValue + ((1772 * uValue) >> 10)).clamp(0, 255);
        
        // Set pixel directly (faster than setPixelRgb)
        image.setPixelRgb(x, y, r, g, b);
      }
    }
    
    stopwatch.stop();
    print('🚀 Optimized YUV conversion: ${stopwatch.elapsedMilliseconds}ms');
    
    return image;
  }
  
  /// Multi-threaded YUV conversion using isolates
  /// Splits work across multiple CPU cores for RK3399
  static Future<img.Image> convertYuv420ToRgbMultiThreaded(CameraImage cameraImage) async {
    final stopwatch = Stopwatch()..start();
    
    final width = cameraImage.width;
    final height = cameraImage.height;
    
    // Determine optimal thread count for RK3399 (6 cores: 2xA72 + 4xA53)
    final numThreads = 4; // Use 4 threads for optimal performance
    final rowsPerThread = height ~/ numThreads;
    
    // Prepare data for isolates
    final yPlane = cameraImage.planes[0].bytes;
    final uPlane = cameraImage.planes[1].bytes;
    final vPlane = cameraImage.planes[2].bytes;
    final yStride = cameraImage.planes[0].bytesPerRow;
    final uStride = cameraImage.planes[1].bytesPerRow;
    
    // Create conversion tasks
    final futures = <Future<Uint8List>>[];
    
    for (int i = 0; i < numThreads; i++) {
      final startRow = i * rowsPerThread;
      final endRow = (i == numThreads - 1) ? height : (i + 1) * rowsPerThread;
      
      final task = {
        'yPlane': yPlane,
        'uPlane': uPlane,
        'vPlane': vPlane,
        'width': width,
        'startRow': startRow,
        'endRow': endRow,
        'yStride': yStride,
        'uStride': uStride,
      };
      
      futures.add(compute(_convertYuvChunk, task));
    }
    
    // Wait for all threads to complete
    final results = await Future.wait(futures);
    
    // Combine results into final image
    final image = img.Image(width: width, height: height);
    
    for (int i = 0; i < results.length; i++) {
      final startRow = i * rowsPerThread;
      final endRow = (i == numThreads - 1) ? height : (i + 1) * rowsPerThread;
      final chunkData = results[i];
      
      // Copy chunk data to final image
      for (int y = startRow; y < endRow; y++) {
        for (int x = 0; x < width; x++) {
          final chunkIndex = ((y - startRow) * width + x) * 3;
          if (chunkIndex + 2 < chunkData.length) {
            final r = chunkData[chunkIndex];
            final g = chunkData[chunkIndex + 1];
            final b = chunkData[chunkIndex + 2];
            image.setPixelRgb(x, y, r, g, b);
          }
        }
      }
    }
    
    stopwatch.stop();
    print('🚀 Multi-threaded YUV conversion: ${stopwatch.elapsedMilliseconds}ms');
    
    return image;
  }
  
  /// Convert YUV chunk in separate isolate
  static Uint8List _convertYuvChunk(Map<String, dynamic> params) {
    final yPlane = params['yPlane'] as Uint8List;
    final uPlane = params['uPlane'] as Uint8List;
    final vPlane = params['vPlane'] as Uint8List;
    final width = params['width'] as int;
    final startRow = params['startRow'] as int;
    final endRow = params['endRow'] as int;
    final yStride = params['yStride'] as int;
    final uStride = params['uStride'] as int;
    
    final chunkHeight = endRow - startRow;
    final rgbChunk = Uint8List(width * chunkHeight * 3);
    
    // Process YUV to RGB for this chunk
    for (int y = startRow; y < endRow; y++) {
      for (int x = 0; x < width; x++) {
        final yIndex = y * yStride + x;
        final uvIndex = (y ~/ 2) * uStride + (x ~/ 2);
        
        if (yIndex < yPlane.length && uvIndex < uPlane.length && uvIndex < vPlane.length) {
          final yValue = yPlane[yIndex];
          final uValue = uPlane[uvIndex] - 128;
          final vValue = vPlane[uvIndex] - 128;
          
          // Optimized YUV to RGB conversion
          final r = (yValue + ((1402 * vValue) >> 10)).clamp(0, 255);
          final g = (yValue - ((344 * uValue + 714 * vValue) >> 10)).clamp(0, 255);
          final b = (yValue + ((1772 * uValue) >> 10)).clamp(0, 255);
          
          final pixelIndex = ((y - startRow) * width + x) * 3;
          rgbChunk[pixelIndex] = r;
          rgbChunk[pixelIndex + 1] = g;
          rgbChunk[pixelIndex + 2] = b;
        }
      }
    }
    
    return rgbChunk;
  }
  
  /// Benchmark different conversion methods
  static Future<void> benchmarkConversions(CameraImage cameraImage) async {
    print('\n🏁 YUV Conversion Benchmark - RK3399 Optimizations');
    print('Image size: ${cameraImage.width}x${cameraImage.height}');
    
    // Test standard Dart conversion
    final standardStopwatch = Stopwatch()..start();
    final standardResult = _convertYuvStandard(cameraImage);
    standardStopwatch.stop();
    
    // Test optimized Dart conversion
    final optimizedStopwatch = Stopwatch()..start();
    final optimizedResult = convertYuv420ToRgbOptimized(cameraImage);
    optimizedStopwatch.stop();
    
    // Test multi-threaded conversion
    final multiThreadStopwatch = Stopwatch()..start();
    final multiThreadResult = await convertYuv420ToRgbMultiThreaded(cameraImage);
    multiThreadStopwatch.stop();
    
    final standardTime = standardStopwatch.elapsedMilliseconds;
    final optimizedTime = optimizedStopwatch.elapsedMilliseconds;
    final multiThreadTime = multiThreadStopwatch.elapsedMilliseconds;
    
    final optimizedSpeedup = standardTime / optimizedTime;
    final multiThreadSpeedup = standardTime / multiThreadTime;
    
    print('📊 Benchmark Results:');
    print('  Standard conversion: ${standardTime}ms');
    print('  Optimized conversion: ${optimizedTime}ms (${optimizedSpeedup.toStringAsFixed(1)}x faster)');
    print('  Multi-threaded conversion: ${multiThreadTime}ms (${multiThreadSpeedup.toStringAsFixed(1)}x faster)');
    print('  Best method: ${multiThreadTime < optimizedTime ? 'Multi-threaded' : 'Optimized'}');
  }
  
  /// Standard YUV conversion for comparison
  static img.Image _convertYuvStandard(CameraImage cameraImage) {
    final width = cameraImage.width;
    final height = cameraImage.height;
    
    final yPlane = cameraImage.planes[0].bytes;
    final uPlane = cameraImage.planes[1].bytes;
    final vPlane = cameraImage.planes[2].bytes;
    
    final image = img.Image(width: width, height: height);
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final yIndex = y * width + x;
        final uvIndex = (y ~/ 2) * (width ~/ 2) + (x ~/ 2);
        
        if (yIndex < yPlane.length && uvIndex < uPlane.length && uvIndex < vPlane.length) {
          final yValue = yPlane[yIndex];
          final uValue = uPlane[uvIndex] - 128;
          final vValue = vPlane[uvIndex] - 128;
          
          final r = (yValue + 1.402 * vValue).clamp(0, 255).toInt();
          final g = (yValue - 0.344 * uValue - 0.714 * vValue).clamp(0, 255).toInt();
          final b = (yValue + 1.772 * uValue).clamp(0, 255).toInt();
          
          image.setPixelRgb(x, y, r, g, b);
        }
      }
    }
    
    return image;
  }
}
