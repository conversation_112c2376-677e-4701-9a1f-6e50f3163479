// Model Selection Widget
import 'package:flutter/material.dart';
import 'package:live_face_detection/models/face_detection_model.dart';
import 'package:live_face_detection/service/unified_face_detector.dart';

/// Widget for selecting and comparing face detection models
class ModelSelectionWidget extends StatefulWidget {
  final UnifiedFaceDetector detector;
  final Function(FaceDetectionModel) onModelChanged;

  const ModelSelectionWidget({
    Key? key,
    required this.detector,
    required this.onModelChanged,
  }) : super(key: key);

  @override
  State<ModelSelectionWidget> createState() => _ModelSelectionWidgetState();
}

class _ModelSelectionWidgetState extends State<ModelSelectionWidget> {
  bool _showPerformanceComparison = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              const Icon(Icons.psychology, color: Colors.blue, size: 24),
              const SizedBox(width: 8),
              const Text(
                'Face Detection Models',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(
                  _showPerformanceComparison ? Icons.expand_less : Icons.expand_more,
                  color: Colors.white,
                ),
                onPressed: () {
                  setState(() {
                    _showPerformanceComparison = !_showPerformanceComparison;
                  });
                },
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Model Selection
          _buildModelSelection(),
          
          const SizedBox(height: 12),
          
          // Current Model Info
          _buildCurrentModelInfo(),
          
          // Performance Comparison (expandable)
          if (_showPerformanceComparison) ...[
            const SizedBox(height: 16),
            _buildPerformanceComparison(),
          ],
        ],
      ),
    );
  }

  Widget _buildModelSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Model:',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: FaceDetectionModel.values.map((model) {
            final isSelected = widget.detector.currentModel == model;
            final specs = ModelSpecs.getSpecs(model);
            
            return GestureDetector(
              onTap: () async {
                if (!isSelected) {
                  final success = await widget.detector.switchModel(model);
                  if (success) {
                    widget.onModelChanged(model);
                    setState(() {});
                  }
                }
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue : Colors.grey[800],
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? Colors.blue : Colors.grey[600]!,
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      model.displayName,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.white70,
                        fontSize: 12,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                    Text(
                      '${specs.inputWidth}×${specs.inputHeight}',
                      style: TextStyle(
                        color: isSelected ? Colors.white70 : Colors.white54,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCurrentModelInfo() {
    final modelInfo = widget.detector.getModelInfo();
    final metrics = widget.detector.getCurrentModelMetrics();
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue, size: 16),
              const SizedBox(width: 6),
              Text(
                modelInfo['name'] ?? 'Unknown',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          // Model specs
          Row(
            children: [
              _buildInfoChip('Input', modelInfo['inputSize'] ?? 'N/A'),
              const SizedBox(width: 8),
              _buildInfoChip('Threshold', '${(modelInfo['threshold'] ?? 0.0).toStringAsFixed(1)}'),
              const SizedBox(width: 8),
              if (metrics['inferenceTime'] != null)
                _buildInfoChip('Time', '${metrics['inferenceTime']}ms'),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Description
          Text(
            modelInfo['description'] ?? '',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 11,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        '$label: $value',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
        ),
      ),
    );
  }

  Widget _buildPerformanceComparison() {
    final comparison = widget.detector.getPerformanceComparison();
    
    if (comparison.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Text(
          'No performance data available yet.\nRun detection to collect metrics.',
          style: TextStyle(color: Colors.white70, fontSize: 12),
          textAlign: TextAlign.center,
        ),
      );
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.speed, color: Colors.green, size: 16),
            SizedBox(width: 6),
            Text(
              'Performance Comparison',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        ...comparison.entries.map((entry) {
          final modelName = entry.key;
          final data = entry.value;
          final isCurrentModel = modelName == widget.detector.currentModel.displayName;
          
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isCurrentModel 
                ? Colors.blue.withOpacity(0.2) 
                : Colors.grey[800],
              borderRadius: BorderRadius.circular(6),
              border: isCurrentModel 
                ? Border.all(color: Colors.blue.withOpacity(0.5))
                : null,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      modelName,
                      style: TextStyle(
                        color: isCurrentModel ? Colors.blue : Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (isCurrentModel) ...[
                      const SizedBox(width: 6),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'ACTIVE',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                
                Row(
                  children: [
                    _buildPerformanceChip('Avg', '${data['averageTime']}ms'),
                    const SizedBox(width: 6),
                    _buildPerformanceChip('Min', '${data['minTime']}ms'),
                    const SizedBox(width: 6),
                    _buildPerformanceChip('Max', '${data['maxTime']}ms'),
                    const SizedBox(width: 6),
                    _buildPerformanceChip('Samples', '${data['sampleCount']}'),
                  ],
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildPerformanceChip(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(3),
      ),
      child: Text(
        '$label: $value',
        style: const TextStyle(
          color: Colors.white70,
          fontSize: 9,
        ),
      ),
    );
  }
}
