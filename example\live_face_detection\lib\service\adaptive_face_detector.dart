// Adaptive Face Detector - Dynamic optimization based on performance
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:image/image.dart' as img;
import 'package:tflite_flutter/tflite_flutter.dart';
import 'package:live_face_detection/models/recognition.dart';
import 'package:live_face_detection/service/optimized_yuv_converter.dart';
import 'package:live_face_detection/service/detailed_performance_tracker.dart';

/// Adaptive Face Detector that adjusts input size based on performance
/// Targets 50ms inference time by dynamically scaling input resolution
class AdaptiveFaceDetector {
  static const String _modelPath = 'assets/civams_models/blazeface.tfl';
  
  // Dynamic input sizes - will be adjusted based on performance
  int _currentInputWidth = 128;
  int _currentInputHeight = 224;
  
  // Performance targets
  static const int _targetInferenceTime = 50; // Target 50ms inference
  static const int _maxInferenceTime = 100;   // Max acceptable 100ms
  static const int _minInferenceTime = 20;    // Min 20ms (don't go smaller)
  
  // Input size options (width, height)
  static const List<List<int>> _inputSizeOptions = [
    [64, 112],   // Ultra small - fastest
    [80, 140],   // Small
    [96, 168],   // Medium-small
    [112, 196],  // Medium
    [128, 224],  // Original - most accurate
  ];
  
  int _currentSizeIndex = 2; // Start with medium-small
  
  Interpreter? _interpreter;
  bool _isInitialized = false;
  bool _isProcessing = false;
  
  // Performance tracking
  final List<int> _recentInferenceTimes = [];
  int _adjustmentCooldown = 0;
  
  /// Initialize adaptive face detector
  Future<bool> initialize() async {
    try {
      print('🔄 Loading BlazeFace model with adaptive sizing...');
      
      // Configure interpreter options
      final options = InterpreterOptions();
      options.threads = 4; // Use 4 CPU threads
      
      _interpreter = await Interpreter.fromAsset(_modelPath, options: options);
      
      // Set initial input size
      _updateInputSize();
      
      _isInitialized = true;
      print('✅ Adaptive BlazeFace detector initialized');
      print('📏 Initial input size: ${_currentInputWidth}x${_currentInputHeight}');
      
      return true;
    } catch (e) {
      print('❌ Failed to initialize adaptive detector: $e');
      return false;
    }
  }
  
  /// Detect faces with adaptive performance optimization
  Future<List<Recognition>> detectFaces(CameraImage cameraImage) async {
    if (!_isInitialized || _interpreter == null || _isProcessing) {
      return [];
    }
    
    _isProcessing = true;
    final totalStopwatch = Stopwatch()..start();
    
    try {
      // Record processing start
      DetailedPerformanceTracker.recordProcessingStart();
      
      // Convert camera image to RGB
      final conversionStopwatch = Stopwatch()..start();
      final rgbImage = await _convertCameraImageToRGB(cameraImage);
      conversionStopwatch.stop();
      DetailedPerformanceTracker.recordStepTime('image_conversion', conversionStopwatch.elapsedMilliseconds);
      
      if (rgbImage == null) return [];
      
      // Resize image to current input size
      final resizeStopwatch = Stopwatch()..start();
      final resized = img.copyResize(rgbImage, width: _currentInputWidth, height: _currentInputHeight);
      resizeStopwatch.stop();
      DetailedPerformanceTracker.recordStepTime('image_resize', resizeStopwatch.elapsedMilliseconds);
      
      // Create input tensor
      final tensorStopwatch = Stopwatch()..start();
      final input = _imageToInputTensor(resized);
      tensorStopwatch.stop();
      DetailedPerformanceTracker.recordStepTime('tensor_creation', tensorStopwatch.elapsedMilliseconds);
      
      // Run inference with timing
      final inferenceStopwatch = Stopwatch()..start();
      final detections = await _runInference(input, cameraImage.width, cameraImage.height);
      inferenceStopwatch.stop();
      final inferenceTime = inferenceStopwatch.elapsedMilliseconds;
      DetailedPerformanceTracker.recordStepTime('inference', inferenceTime);
      
      // Record detection results
      DetailedPerformanceTracker.recordDetectionResults(detections.length);
      
      // Adapt input size based on performance
      _adaptInputSize(inferenceTime);
      
      totalStopwatch.stop();
      
      // Debug logging
      print('🎯 Adaptive detection: ${detections.length} faces, ${inferenceTime}ms inference, ${_currentInputWidth}x${_currentInputHeight}');
      
      return detections;
    } catch (e) {
      print('❌ Error in adaptive face detection: $e');
      return [];
    } finally {
      _isProcessing = false;
    }
  }
  
  /// Adapt input size based on inference performance
  void _adaptInputSize(int inferenceTime) {
    // Add to recent times
    _recentInferenceTimes.add(inferenceTime);
    if (_recentInferenceTimes.length > 5) {
      _recentInferenceTimes.removeAt(0);
    }
    
    // Cooldown to prevent rapid changes
    if (_adjustmentCooldown > 0) {
      _adjustmentCooldown--;
      return;
    }
    
    // Need at least 3 samples to make decision
    if (_recentInferenceTimes.length < 3) return;
    
    // Calculate average recent inference time
    final avgInferenceTime = _recentInferenceTimes.reduce((a, b) => a + b) / _recentInferenceTimes.length;
    
    bool sizeChanged = false;
    
    // If too slow, reduce input size
    if (avgInferenceTime > _maxInferenceTime && _currentSizeIndex > 0) {
      _currentSizeIndex--;
      sizeChanged = true;
      print('📉 Reducing input size due to slow inference (${avgInferenceTime.toStringAsFixed(1)}ms avg)');
    }
    // If too fast and accuracy might be suffering, increase input size
    else if (avgInferenceTime < _minInferenceTime && _currentSizeIndex < _inputSizeOptions.length - 1) {
      _currentSizeIndex++;
      sizeChanged = true;
      print('📈 Increasing input size due to fast inference (${avgInferenceTime.toStringAsFixed(1)}ms avg)');
    }
    // If within target range but closer to max, slightly reduce
    else if (avgInferenceTime > _targetInferenceTime && avgInferenceTime <= _maxInferenceTime && _currentSizeIndex > 0) {
      _currentSizeIndex--;
      sizeChanged = true;
      print('🎯 Fine-tuning: reducing input size (${avgInferenceTime.toStringAsFixed(1)}ms avg)');
    }
    
    if (sizeChanged) {
      _updateInputSize();
      _adjustmentCooldown = 10; // Wait 10 frames before next adjustment
      _recentInferenceTimes.clear(); // Reset history after change
    }
  }
  
  /// Update current input size from size index
  void _updateInputSize() {
    final size = _inputSizeOptions[_currentSizeIndex];
    _currentInputWidth = size[0];
    _currentInputHeight = size[1];
    
    print('📏 Input size updated: ${_currentInputWidth}x${_currentInputHeight}');
  }
  
  /// Convert camera image to RGB
  Future<img.Image?> _convertCameraImageToRGB(CameraImage cameraImage) async {
    try {
      if (cameraImage.planes.isEmpty || cameraImage.planes.length < 3) {
        return null;
      }
      
      return OptimizedYuvConverter.convertYuv420ToRgbOptimized(cameraImage);
    } catch (e) {
      print('Error converting camera image: $e');
      return null;
    }
  }
  
  /// Convert image to input tensor
  List<List<List<List<double>>>> _imageToInputTensor(img.Image image) {
    final input = List.generate(1, (_) =>
      List.generate(_currentInputHeight, (y) =>
        List.generate(_currentInputWidth, (x) =>
          List.generate(3, (c) {
            final pixel = image.getPixel(x, y);
            switch (c) {
              case 0: return pixel.r / 255.0; // Red
              case 1: return pixel.g / 255.0; // Green  
              case 2: return pixel.b / 255.0; // Blue
              default: return 0.0;
            }
          })
        )
      )
    );
    return input;
  }
  
  /// Run inference on input tensor
  Future<List<Recognition>> _runInference(
    List<List<List<List<double>>>> input,
    int originalWidth,
    int originalHeight,
  ) async {
    try {
      // Set input tensor
      _interpreter!.setInputTensor(0, input);
      
      // Run inference
      _interpreter!.invoke();
      
      // Get outputs
      final output1 = _interpreter!.getOutputTensor(0);
      final output2 = _interpreter!.getOutputTensor(1);
      
      // Process results
      return _processBlazeFaceResults(
        output1.cast<List<double>>(),
        output2.cast<List<double>>(),
        originalWidth,
        originalHeight,
      );
    } catch (e) {
      print('Error running inference: $e');
      return [];
    }
  }
  
  /// Process BlazeFace inference results
  List<Recognition> _processBlazeFaceResults(
    List<List<double>> detections,
    List<List<double>> scores,
    int originalWidth,
    int originalHeight,
  ) {
    final results = <Recognition>[];
    
    for (int i = 0; i < detections.length; i++) {
      final detection = detections[i];
      final score = scores[i][0];
      
      if (score > 0.1) { // Low threshold for debugging
        // Scale coordinates back to original image size
        final x1 = (detection[0] * originalWidth).clamp(0, originalWidth.toDouble());
        final y1 = (detection[1] * originalHeight).clamp(0, originalHeight.toDouble());
        final x2 = (detection[2] * originalWidth).clamp(0, originalWidth.toDouble());
        final y2 = (detection[3] * originalHeight).clamp(0, originalHeight.toDouble());
        
        final rect = Rect.fromLTRB(x1, y1, x2, y2);
        
        results.add(Recognition(
          i,        // id
          'face',   // label
          score,    // score
          rect,     // location
        ));
      }
    }
    
    // Sort by confidence
    results.sort((a, b) => b.score!.compareTo(a.score!));
    return results.take(10).toList();
  }
  
  /// Get current performance info
  Map<String, dynamic> getPerformanceInfo() {
    final avgInferenceTime = _recentInferenceTimes.isEmpty 
      ? 0.0 
      : _recentInferenceTimes.reduce((a, b) => a + b) / _recentInferenceTimes.length;
    
    return {
      'currentInputSize': '${_currentInputWidth}x${_currentInputHeight}',
      'sizeIndex': _currentSizeIndex,
      'avgInferenceTime': avgInferenceTime.round(),
      'targetInferenceTime': _targetInferenceTime,
      'recentSamples': _recentInferenceTimes.length,
      'cooldown': _adjustmentCooldown,
    };
  }
  
  /// Dispose resources
  void dispose() {
    _interpreter?.close();
    _isInitialized = false;
  }
}
