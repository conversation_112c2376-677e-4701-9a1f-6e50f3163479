// MediaPipe Face Detector - Google's optimized face detection
import 'dart:io';
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:live_face_detection/models/recognition.dart';
import 'package:live_face_detection/service/optimized_yuv_converter.dart';
import 'package:live_face_detection/service/detailed_performance_tracker.dart';
import 'package:live_face_detection/service/lightweight_performance_tracker.dart';

/// MediaPipe Face Detector for performance comparison with BlazeFace
class MediaPipeFaceDetector {
  FaceDetector? _faceDetector;
  bool _isInitialized = false;
  bool _isProcessing = false;
  
  // Performance tracking
  int _inferenceTime = 0;
  
  /// Initialize MediaPipe face detector
  Future<bool> initialize() async {
    try {
      print('🔄 Initializing MediaPipe Face Detector...');
      
      // Configure MediaPipe face detection options
      final options = FaceDetectorOptions(
        enableContours: false,        // Disable contours for speed
        enableLandmarks: false,       // Disable landmarks for speed
        enableClassification: false,  // Disable classification for speed
        enableTracking: true,        // Disable tracking for speed
        minFaceSize: 0.1,            // Minimum face size (10% of image)
        performanceMode: FaceDetectorMode.fast, // Optimize for speed
      );
      
      _faceDetector = FaceDetector(options: options);
      _isInitialized = true;
      
      print('✅ MediaPipe Face Detector initialized');
      print('📊 Configuration:');
      print('  Performance Mode: FAST');
      print('  Min Face Size: 10%');
      print('  Features: Detection only (no landmarks/contours)');
      
      return true;
    } catch (e) {
      print('❌ Failed to initialize MediaPipe detector: $e');
      return false;
    }
  }
  
  /// Detect faces using MediaPipe
  Future<List<Recognition>> detectFaces(CameraImage cameraImage) async {
    if (!_isInitialized || _faceDetector == null || _isProcessing) {
      return [];
    }
    
    _isProcessing = true;
    final totalStopwatch = Stopwatch()..start();
    
    try {
      // Record processing start
      DetailedPerformanceTracker.recordProcessingStart();
      LightweightPerformanceTracker.recordProcessedFrame();
      
      // Step 1: Convert camera image to InputImage format
      final conversionStopwatch = Stopwatch()..start();
      final inputImage = await _convertCameraImageToInputImage(cameraImage);
      conversionStopwatch.stop();
      DetailedPerformanceTracker.recordStepTime('image_conversion', conversionStopwatch.elapsedMilliseconds);
      
      if (inputImage == null) {
        print('❌ Failed to convert camera image');
        return [];
      }
      
      // Step 2: Run MediaPipe face detection
      final inferenceStopwatch = Stopwatch()..start();
      final faces = await _faceDetector!.processImage(inputImage);
      inferenceStopwatch.stop();
      _inferenceTime = inferenceStopwatch.elapsedMilliseconds;
      DetailedPerformanceTracker.recordStepTime('inference', _inferenceTime);
      LightweightPerformanceTracker.recordInferenceTime(_inferenceTime);
      
      // Step 3: Convert MediaPipe faces to Recognition objects
      final processingStopwatch = Stopwatch()..start();
      final results = _convertFacesToRecognitions(faces, cameraImage.width, cameraImage.height);
      processingStopwatch.stop();
      DetailedPerformanceTracker.recordStepTime('post_processing', processingStopwatch.elapsedMilliseconds);
      
      // Record detection results
      DetailedPerformanceTracker.recordDetectionResults(results.length);
      
      totalStopwatch.stop();
      
      // Debug logging
      print('🎯 MediaPipe: ${results.length} faces detected in ${_inferenceTime}ms');
      if (results.isNotEmpty) {
        for (int i = 0; i < results.length; i++) {
          final face = results[i];
          print('  Face $i: confidence=${face.score.toStringAsFixed(2)}, bbox=${face.location}');
        }
      }
      
      return results;
      
    } catch (e) {
      print('❌ Error in MediaPipe face detection: $e');
      return [];
    } finally {
      _isProcessing = false;
    }
  }
  
  /// Convert CameraImage to InputImage for MediaPipe
  Future<InputImage?> _convertCameraImageToInputImage(CameraImage cameraImage) async {
    try {
      // Convert YUV to RGB first
      final rgbImage = OptimizedYuvConverter.convertYuv420ToRgbOptimized(cameraImage);
      
      // Convert to bytes
      final bytes = Uint8List.fromList(rgbImage.getBytes());
      
      // Create InputImage
      final inputImage = InputImage.fromBytes(
        bytes: bytes,
        metadata: InputImageMetadata(
          size: Size(cameraImage.width.toDouble(), cameraImage.height.toDouble()),
          rotation: _getInputImageRotation(),
          format: InputImageFormat.bgra8888, // Use available format
          bytesPerRow: cameraImage.width * 4, // BGRA = 4 bytes per pixel
        ),
      );
      
      return inputImage;
    } catch (e) {
      print('❌ Error converting to InputImage: $e');
      return null;
    }
  }
  
  /// Convert MediaPipe Face objects to Recognition objects
  List<Recognition> _convertFacesToRecognitions(List<Face> faces, int imageWidth, int imageHeight) {
    final results = <Recognition>[];
    
    for (int i = 0; i < faces.length; i++) {
      final face = faces[i];
      final boundingBox = face.boundingBox;
      
      // MediaPipe provides confidence as trackingId reliability
      // For face detection, we'll use a default high confidence
      final confidence = 0.9; // MediaPipe faces are generally high quality
      
      // Create Recognition object
      final recognition = Recognition(
        i,                    // id
        'face',              // label
        confidence,          // score
        Rect.fromLTRB(       // location
          boundingBox.left,
          boundingBox.top,
          boundingBox.right,
          boundingBox.bottom,
        ),
      );
      
      results.add(recognition);
    }
    
    return results;
  }
  
  /// Get current inference time
  int get inferenceTime => _inferenceTime;
  
  /// Check if detector is initialized
  bool get isInitialized => _isInitialized;
  
  /// Check if detector is processing
  bool get isProcessing => _isProcessing;
  
  /// Get detector info
  Map<String, dynamic> getDetectorInfo() {
    return {
      'name': 'MediaPipe Face Detection',
      'version': 'Google ML Kit',
      'initialized': _isInitialized,
      'processing': _isProcessing,
      'lastInferenceTime': _inferenceTime,
      'features': {
        'contours': false,
        'landmarks': false,
        'classification': false,
        'tracking': false,
      },
      'performance': 'FAST',
      'minFaceSize': '10%',
    };
  }
  
  /// Benchmark MediaPipe performance
  Future<Map<String, dynamic>> benchmark(CameraImage cameraImage, {int iterations = 10}) async {
    if (!_isInitialized) {
      return {'error': 'Detector not initialized'};
    }
    
    print('🏁 MediaPipe Benchmark - ${iterations} iterations');
    
    final times = <int>[];
    final detectionCounts = <int>[];
    
    for (int i = 0; i < iterations; i++) {
      final stopwatch = Stopwatch()..start();
      final results = await detectFaces(cameraImage);
      stopwatch.stop();
      
      times.add(stopwatch.elapsedMilliseconds);
      detectionCounts.add(results.length);
      
      // Small delay between iterations
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    final avgTime = times.reduce((a, b) => a + b) / times.length;
    final minTime = times.reduce((a, b) => a < b ? a : b);
    final maxTime = times.reduce((a, b) => a > b ? a : b);
    final avgDetections = detectionCounts.reduce((a, b) => a + b) / detectionCounts.length;
    
    final benchmarkResults = {
      'detector': 'MediaPipe',
      'iterations': iterations,
      'avgTime': avgTime.round(),
      'minTime': minTime,
      'maxTime': maxTime,
      'avgDetections': avgDetections,
      'fps': (1000 / avgTime).toStringAsFixed(1),
      'times': times,
      'detectionCounts': detectionCounts,
    };
    
    print('📊 MediaPipe Benchmark Results:');
    print('  Average Time: ${avgTime.round()}ms');
    print('  Min/Max Time: ${minTime}ms / ${maxTime}ms');
    print('  Average FPS: ${(1000 / avgTime).toStringAsFixed(1)}');
    print('  Average Detections: ${avgDetections.toStringAsFixed(1)}');
    
    return benchmarkResults;
  }

  /// Get proper InputImageRotation for current device orientation
  InputImageRotation _getInputImageRotation() {
    // For Android devices in portrait mode, camera stream is typically landscape
    // and needs 90° rotation to match screen orientation
    if (Platform.isAndroid) {
      // Assume portrait mode (most common use case)
      return InputImageRotation.rotation90deg;
    } else {
      // iOS handles orientation differently
      return InputImageRotation.rotation0deg;
    }
  }

  /// Dispose resources
  void dispose() {
    _faceDetector?.close();
    _isInitialized = false;
  }
}
