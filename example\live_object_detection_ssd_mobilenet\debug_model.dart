import 'dart:io';
import 'package:tflite_flutter/tflite_flutter.dart';

void main() async {
  print('Loading model...');
  
  try {
    final interpreter = await Interpreter.fromAsset('assets/models/ssd_mobilenet.tflite');
    
    print('\n=== MODEL INFO ===');
    print('Input tensors: ${interpreter.getInputTensors().length}');
    print('Output tensors: ${interpreter.getOutputTensors().length}');
    
    print('\n=== INPUT TENSORS ===');
    for (int i = 0; i < interpreter.getInputTensors().length; i++) {
      final tensor = interpreter.getInputTensor(i);
      print('Input $i: ${tensor.shape} (${tensor.type})');
    }
    
    print('\n=== OUTPUT TENSORS ===');
    for (int i = 0; i < interpreter.getOutputTensors().length; i++) {
      final tensor = interpreter.getOutputTensor(i);
      print('Output $i: ${tensor.shape} (${tensor.type})');
    }
    
    interpreter.close();
    print('\nModel inspection completed!');
    
  } catch (e) {
    print('Error: $e');
  }
}
