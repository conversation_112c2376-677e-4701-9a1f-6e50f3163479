// Detector Comparison Service - Compare BlazeFace vs MediaPipe performance
import 'package:camera/camera.dart';
import 'package:live_face_detection/models/recognition.dart';
import 'package:live_face_detection/service/simple_face_detector.dart';
import 'package:live_face_detection/service/mediapipe_face_detector.dart';

/// Service to compare different face detection models
class DetectorComparisonService {
  SimpleFaceDetector? _blazeFaceDetector;
  MediaPipeFaceDetector? _mediaPipeDetector;
  
  DetectorType _currentDetector = DetectorType.blazeFace;
  bool _isInitialized = false;
  
  // Performance comparison data
  final Map<DetectorType, List<int>> _performanceHistory = {
    DetectorType.blazeFace: [],
    DetectorType.mediaPipe: [],
  };
  
  /// Initialize both detectors
  Future<bool> initialize() async {
    try {
      print('🔄 Initializing detector comparison service...');
      
      // Initialize BlazeFace
      _blazeFaceDetector = SimpleFaceDetector();
      final blazeFaceSuccess = await _blazeFaceDetector!.initialize();
      
      // Initialize MediaPipe
      _mediaPipeDetector = MediaPipeFaceDetector();
      final mediaPipeSuccess = await _mediaPipeDetector!.initialize();
      
      _isInitialized = blazeFaceSuccess && mediaPipeSuccess;
      
      if (_isInitialized) {
        print('✅ Both detectors initialized successfully');
        print('📊 Available detectors:');
        print('  - BlazeFace (TensorFlow Lite)');
        print('  - MediaPipe (Google ML Kit)');
      } else {
        print('❌ Failed to initialize one or more detectors');
        print('  BlazeFace: ${blazeFaceSuccess ? 'OK' : 'FAILED'}');
        print('  MediaPipe: ${mediaPipeSuccess ? 'OK' : 'FAILED'}');
      }
      
      return _isInitialized;
    } catch (e) {
      print('❌ Error initializing comparison service: $e');
      return false;
    }
  }
  
  /// Detect faces using current detector
  Future<List<Recognition>> detectFaces(CameraImage cameraImage) async {
    if (!_isInitialized) return [];
    
    final stopwatch = Stopwatch()..start();
    List<Recognition> results = [];
    
    try {
      switch (_currentDetector) {
        case DetectorType.blazeFace:
          if (_blazeFaceDetector != null) {
            results = await _blazeFaceDetector!.detectFaces(cameraImage);
          }
          break;
        case DetectorType.mediaPipe:
          if (_mediaPipeDetector != null) {
            results = await _mediaPipeDetector!.detectFaces(cameraImage);
          }
          break;
      }
      
      stopwatch.stop();
      
      // Record performance
      _recordPerformance(_currentDetector, stopwatch.elapsedMilliseconds);
      
      return results;
    } catch (e) {
      print('❌ Error in face detection: $e');
      return [];
    }
  }
  
  /// Switch between detectors
  void switchDetector(DetectorType detectorType) {
    if (_currentDetector != detectorType) {
      _currentDetector = detectorType;
      print('🔄 Switched to ${detectorType.name} detector');
    }
  }
  
  /// Get current detector type
  DetectorType get currentDetector => _currentDetector;
  
  /// Record performance data
  void _recordPerformance(DetectorType detector, int timeMs) {
    final history = _performanceHistory[detector]!;
    history.add(timeMs);
    
    // Keep only recent 50 samples
    if (history.length > 50) {
      history.removeAt(0);
    }
  }
  
  /// Get performance comparison
  Map<String, dynamic> getPerformanceComparison() {
    final comparison = <String, dynamic>{};
    
    for (final detector in DetectorType.values) {
      final history = _performanceHistory[detector]!;
      
      if (history.isNotEmpty) {
        final avgTime = history.reduce((a, b) => a + b) / history.length;
        final minTime = history.reduce((a, b) => a < b ? a : b);
        final maxTime = history.reduce((a, b) => a > b ? a : b);
        final fps = 1000 / avgTime;
        
        comparison[detector.name] = {
          'avgTime': avgTime.round(),
          'minTime': minTime,
          'maxTime': maxTime,
          'fps': fps.toStringAsFixed(1),
          'samples': history.length,
          'current': detector == _currentDetector,
        };
      } else {
        comparison[detector.name] = {
          'avgTime': 0,
          'minTime': 0,
          'maxTime': 0,
          'fps': '0.0',
          'samples': 0,
          'current': detector == _currentDetector,
        };
      }
    }
    
    return comparison;
  }
  
  /// Print performance comparison
  void printPerformanceComparison() {
    final comparison = getPerformanceComparison();
    
    print('\n📊 DETECTOR PERFORMANCE COMPARISON');
    print('=' * 50);
    
    for (final entry in comparison.entries) {
      final detector = entry.key;
      final data = entry.value as Map<String, dynamic>;
      final current = data['current'] as bool;
      
      print('${current ? '👉' : '  '} $detector:');
      print('    Average Time: ${data['avgTime']}ms');
      print('    Min/Max Time: ${data['minTime']}ms / ${data['maxTime']}ms');
      print('    Average FPS: ${data['fps']}');
      print('    Samples: ${data['samples']}');
      print('');
    }
    
    // Performance winner
    final blazeFaceAvg = comparison['blazeFace']?['avgTime'] ?? 999999;
    final mediaPipeAvg = comparison['mediaPipe']?['avgTime'] ?? 999999;
    
    if (blazeFaceAvg < mediaPipeAvg) {
      final speedup = (mediaPipeAvg / blazeFaceAvg).toStringAsFixed(1);
      print('🏆 Winner: BlazeFace (${speedup}x faster)');
    } else if (mediaPipeAvg < blazeFaceAvg) {
      final speedup = (blazeFaceAvg / mediaPipeAvg).toStringAsFixed(1);
      print('🏆 Winner: MediaPipe (${speedup}x faster)');
    } else {
      print('🤝 Tie: Similar performance');
    }
    
    print('=' * 50);
  }
  
  /// Run comprehensive benchmark
  Future<Map<String, dynamic>> runBenchmark(CameraImage cameraImage, {int iterations = 20}) async {
    if (!_isInitialized) {
      return {'error': 'Service not initialized'};
    }
    
    print('🏁 COMPREHENSIVE DETECTOR BENCHMARK');
    print('Testing both detectors with $iterations iterations each...');
    
    final results = <String, dynamic>{};
    
    // Benchmark BlazeFace
    if (_blazeFaceDetector != null) {
      print('\n🔄 Benchmarking BlazeFace...');
      switchDetector(DetectorType.blazeFace);
      
      final blazeFaceTimes = <int>[];
      final blazeFaceDetections = <int>[];
      
      for (int i = 0; i < iterations; i++) {
        final stopwatch = Stopwatch()..start();
        final detections = await detectFaces(cameraImage);
        stopwatch.stop();
        
        blazeFaceTimes.add(stopwatch.elapsedMilliseconds);
        blazeFaceDetections.add(detections.length);
        
        await Future.delayed(const Duration(milliseconds: 50));
      }
      
      results['blazeFace'] = _calculateBenchmarkStats(blazeFaceTimes, blazeFaceDetections);
    }
    
    // Benchmark MediaPipe
    if (_mediaPipeDetector != null) {
      print('\n🔄 Benchmarking MediaPipe...');
      switchDetector(DetectorType.mediaPipe);
      
      final mediaPipeTimes = <int>[];
      final mediaPipeDetections = <int>[];
      
      for (int i = 0; i < iterations; i++) {
        final stopwatch = Stopwatch()..start();
        final detections = await detectFaces(cameraImage);
        stopwatch.stop();
        
        mediaPipeTimes.add(stopwatch.elapsedMilliseconds);
        mediaPipeDetections.add(detections.length);
        
        await Future.delayed(const Duration(milliseconds: 50));
      }
      
      results['mediaPipe'] = _calculateBenchmarkStats(mediaPipeTimes, mediaPipeDetections);
    }
    
    // Print comparison
    _printBenchmarkResults(results);
    
    return results;
  }
  
  /// Calculate benchmark statistics
  Map<String, dynamic> _calculateBenchmarkStats(List<int> times, List<int> detections) {
    final avgTime = times.reduce((a, b) => a + b) / times.length;
    final minTime = times.reduce((a, b) => a < b ? a : b);
    final maxTime = times.reduce((a, b) => a > b ? a : b);
    final avgDetections = detections.reduce((a, b) => a + b) / detections.length;
    final fps = 1000 / avgTime;
    
    return {
      'avgTime': avgTime.round(),
      'minTime': minTime,
      'maxTime': maxTime,
      'avgDetections': avgDetections,
      'fps': fps.toStringAsFixed(1),
      'times': times,
      'detections': detections,
    };
  }
  
  /// Print benchmark results
  void _printBenchmarkResults(Map<String, dynamic> results) {
    print('\n📊 BENCHMARK RESULTS SUMMARY');
    print('=' * 60);
    
    final blazeFace = results['blazeFace'];
    final mediaPipe = results['mediaPipe'];
    
    if (blazeFace != null) {
      print('🔥 BlazeFace (TensorFlow Lite):');
      print('  Average Time: ${blazeFace['avgTime']}ms');
      print('  Min/Max Time: ${blazeFace['minTime']}ms / ${blazeFace['maxTime']}ms');
      print('  Average FPS: ${blazeFace['fps']}');
      print('  Average Detections: ${blazeFace['avgDetections'].toStringAsFixed(1)}');
    }
    
    if (mediaPipe != null) {
      print('\n🎯 MediaPipe (Google ML Kit):');
      print('  Average Time: ${mediaPipe['avgTime']}ms');
      print('  Min/Max Time: ${mediaPipe['minTime']}ms / ${mediaPipe['maxTime']}ms');
      print('  Average FPS: ${mediaPipe['fps']}');
      print('  Average Detections: ${mediaPipe['avgDetections'].toStringAsFixed(1)}');
    }
    
    // Winner analysis
    if (blazeFace != null && mediaPipe != null) {
      final blazeTime = blazeFace['avgTime'] as int;
      final mediaTime = mediaPipe['avgTime'] as int;
      
      print('\n🏆 PERFORMANCE WINNER:');
      if (blazeTime < mediaTime) {
        final speedup = (mediaTime / blazeTime).toStringAsFixed(1);
        print('  BlazeFace wins by ${speedup}x (${mediaTime - blazeTime}ms faster)');
      } else if (mediaTime < blazeTime) {
        final speedup = (blazeTime / mediaTime).toStringAsFixed(1);
        print('  MediaPipe wins by ${speedup}x (${blazeTime - mediaTime}ms faster)');
      } else {
        print('  Tie - Similar performance');
      }
    }
    
    print('=' * 60);
  }
  
  /// Dispose resources
  void dispose() {
    _blazeFaceDetector?.dispose();
    _mediaPipeDetector?.dispose();
    _isInitialized = false;
  }
}

/// Detector types enum
enum DetectorType {
  blazeFace,
  mediaPipe,
}

extension DetectorTypeExtension on DetectorType {
  String get name {
    switch (this) {
      case DetectorType.blazeFace:
        return 'BlazeFace';
      case DetectorType.mediaPipe:
        return 'MediaPipe';
    }
  }
  
  String get description {
    switch (this) {
      case DetectorType.blazeFace:
        return 'TensorFlow Lite BlazeFace';
      case DetectorType.mediaPipe:
        return 'Google MediaPipe ML Kit';
    }
  }
}
