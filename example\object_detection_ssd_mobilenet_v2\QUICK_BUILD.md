# Hướng dẫn Build APK nhanh

## ✅ Đ<PERSON> hoàn thiện nâng cấp Gradle và Dependencies!

### Build Tools (Plugin DSL):
- Gradle version: 7.5 → 8.10.2 (latest stable)
- Android Gradle Plugin: 7.3.0 → 8.7.2 (kh<PERSON><PERSON> phụ<PERSON> bug Java 21)
- Java version: 1.8 → 17
- Kotlin version: 1.7.10 → 2.0.21 (latest stable)
- NDK version: 26.3.11579264 → 27.0.12077973
- compileSdk & targetSdk: 35 (Android 15)

### Dependencies:
- tflite_flutter: 0.10.4 → 0.11.0 (fixes UnmodifiableUint8ListView error)
- image_picker: 0.8.8 → 1.1.2 (latest stable)
- image: 4.0.17 → 4.2.0 (latest stable)

### Architecture:
- Chuyển từ imperative apply sang Plugin DSL syntax
- Cập nhật settings.gradle với pluginManagement và plugins blocks
- Xóa buildscript block cũ và sử dụng plugins block mới

## Cách 1: Sử dụng Command Prompt (Đ<PERSON>n gi<PERSON> nhất)

1. **Mở Command Prompt** (cmd) v<PERSON><PERSON> quyền Administrator
2. **Copy và paste từng lệnh sau:**

```cmd
cd /d "c:\Users\<USER>\workspace\flutter-tflite\example\object_detection_ssd_mobilenet_v2"
```

```cmd
flutter clean
flutter pub cache clean
```

```cmd
flutter pub get
```

```cmd
flutter build apk --release
```

3. **Tìm file APK tại:** `build\app\outputs\flutter-apk\app-release.apk`

## Cách 2: Sử dụng PowerShell

1. **Mở PowerShell** với quyền Administrator
2. **Chạy lệnh:**

```powershell
Set-Location "c:\Users\<USER>\workspace\flutter-tflite\example\object_detection_ssd_mobilenet_v2"
flutter pub get
flutter build apk --release
```

## Cách 3: Sử dụng Android Studio

1. Mở Android Studio
2. Open project → chọn thư mục `c:\Users\<USER>\workspace\flutter-tflite\example\object_detection_ssd_mobilenet_v2`
3. Mở Terminal trong Android Studio
4. Chạy: `flutter build apk --release`

## Kiểm tra kết quả

Sau khi build thành công, bạn sẽ thấy:
- File APK tại: `build\app\outputs\flutter-apk\app-release.apk`
- Kích thước file khoảng 20-50MB
- Thông báo "Built build\app\outputs\flutter-apk\app-release.apk"

## Xử lý lỗi thường gặp

### Lỗi "Flutter not found"
```cmd
flutter doctor
```
Nếu lỗi, cài đặt lại Flutter hoặc thêm vào PATH

### Lỗi dependencies
```cmd
flutter clean
flutter pub get
```

### Lỗi Android SDK
```cmd
flutter doctor --android-licenses
```

## Cài đặt APK

1. Copy file APK vào điện thoại Android
2. Bật "Unknown sources" trong Settings
3. Tap vào file APK để cài đặt
4. Mở app và test với camera hoặc ảnh từ thư viện
