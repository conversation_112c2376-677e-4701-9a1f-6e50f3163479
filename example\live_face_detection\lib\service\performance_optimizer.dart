// Performance Optimization Service
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:image/image.dart' as img;

/// Performance optimization utilities for face detection
class PerformanceOptimizer {
  
  /// Fast YUV420 to RGB conversion with reduced allocations
  static img.Image fastYUV420ToRGB(CameraImage cameraImage) {
    final width = cameraImage.width;
    final height = cameraImage.height;
    
    final yPlane = cameraImage.planes[0].bytes;
    final uPlane = cameraImage.planes[1].bytes;
    final vPlane = cameraImage.planes[2].bytes;
    
    // Pre-allocate pixel buffer
    final pixels = Uint8List(width * height * 4); // RGBA
    
    // Optimized conversion with lookup tables
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final yIndex = y * width + x;
        final uvIndex = (y ~/ 2) * (width ~/ 2) + (x ~/ 2);
        
        if (yIndex < yPlane.length && uvIndex < uPlane.length && uvIndex < vPlane.length) {
          final yValue = yPlane[yIndex];
          final uValue = uPlane[uvIndex] - 128;
          final vValue = vPlane[uvIndex] - 128;
          
          // Fast YUV to RGB conversion with bit operations
          final r = _clampByte(yValue + ((vValue * 1436) >> 10));
          final g = _clampByte(yValue - ((uValue * 352 + vValue * 731) >> 10));
          final b = _clampByte(yValue + ((uValue * 1814) >> 10));
          
          final pixelIndex = yIndex * 4;
          pixels[pixelIndex] = r;     // R
          pixels[pixelIndex + 1] = g; // G
          pixels[pixelIndex + 2] = b; // B
          pixels[pixelIndex + 3] = 255; // A
        }
      }
    }
    
    return img.Image.fromBytes(
      width: width,
      height: height,
      bytes: pixels.buffer,
      format: img.Format.uint8,
      numChannels: 4,
    );
  }
  
  /// Fast resize with bilinear interpolation
  static img.Image fastResize(img.Image src, int targetWidth, int targetHeight) {
    if (src.width == targetWidth && src.height == targetHeight) {
      return src;
    }
    
    final dst = img.Image(width: targetWidth, height: targetHeight);
    
    final scaleX = src.width / targetWidth;
    final scaleY = src.height / targetHeight;
    
    for (int y = 0; y < targetHeight; y++) {
      for (int x = 0; x < targetWidth; x++) {
        final srcX = (x * scaleX).floor();
        final srcY = (y * scaleY).floor();
        
        if (srcX < src.width && srcY < src.height) {
          final pixel = src.getPixel(srcX, srcY);
          dst.setPixel(x, y, pixel);
        }
      }
    }
    
    return dst;
  }
  
  /// Optimized tensor conversion with pre-allocated buffers
  static List<List<List<List<double>>>> imageToTensorOptimized(
    img.Image image,
    int targetWidth,
    int targetHeight,
  ) {
    // Pre-allocate tensor structure
    final tensor = List.generate(1, (_) =>
      List.generate(targetHeight, (_) =>
        List.generate(targetWidth, (_) =>
          List.filled(3, 0.0)
        )
      )
    );
    
    // Fast pixel access and normalization
    for (int y = 0; y < targetHeight; y++) {
      for (int x = 0; x < targetWidth; x++) {
        final pixel = image.getPixel(x, y);
        
        // Direct assignment without switch statement
        tensor[0][y][x][0] = pixel.r / 255.0; // Red
        tensor[0][y][x][1] = pixel.g / 255.0; // Green
        tensor[0][y][x][2] = pixel.b / 255.0; // Blue
      }
    }
    
    return tensor;
  }
  
  /// Downsample image for faster processing
  static img.Image downsampleImage(img.Image src, double factor) {
    if (factor >= 1.0) return src;
    
    final newWidth = (src.width * factor).round();
    final newHeight = (src.height * factor).round();
    
    return fastResize(src, newWidth, newHeight);
  }
  
  /// Clamp byte value efficiently
  static int _clampByte(int value) {
    return value < 0 ? 0 : (value > 255 ? 255 : value);
  }
  
  /// Performance monitoring
  static class PerformanceMonitor {
    static final Map<String, List<int>> _metrics = {};
    
    static void recordTime(String operation, int timeMs) {
      _metrics.putIfAbsent(operation, () => []);
      final list = _metrics[operation]!;
      list.add(timeMs);
      
      // Keep only last 50 measurements
      if (list.length > 50) {
        list.removeAt(0);
      }
    }
    
    static Map<String, double> getAverages() {
      final averages = <String, double>{};
      
      _metrics.forEach((operation, times) {
        if (times.isNotEmpty) {
          final avg = times.reduce((a, b) => a + b) / times.length;
          averages[operation] = avg;
        }
      });
      
      return averages;
    }
    
    static void printStats() {
      final averages = getAverages();
      print('\n=== PERFORMANCE STATS ===');
      averages.forEach((operation, avg) {
        print('$operation: ${avg.toStringAsFixed(1)}ms avg');
      });
    }
  }
}

/// Frame rate controller for consistent performance
class FrameRateController {
  static const int _targetFPS = 15; // Target 15 FPS for better performance
  static const int _frameIntervalMs = 1000 ~/ _targetFPS;
  
  DateTime _lastFrameTime = DateTime.now();
  
  /// Check if enough time has passed for next frame
  bool shouldProcessFrame() {
    final now = DateTime.now();
    final elapsed = now.difference(_lastFrameTime).inMilliseconds;
    
    if (elapsed >= _frameIntervalMs) {
      _lastFrameTime = now;
      return true;
    }
    
    return false;
  }
  
  /// Get current effective FPS
  double getCurrentFPS() {
    return 1000.0 / _frameIntervalMs;
  }
}

/// Memory pool for reusing objects
class ObjectPool<T> {
  final List<T> _pool = [];
  final T Function() _factory;
  
  ObjectPool(this._factory);
  
  T acquire() {
    if (_pool.isNotEmpty) {
      return _pool.removeLast();
    }
    return _factory();
  }
  
  void release(T object) {
    if (_pool.length < 10) { // Limit pool size
      _pool.add(object);
    }
  }
}

/// Optimized detection configuration
class OptimizedDetectionConfig {
  // Reduced input sizes for faster processing
  static const Map<String, Map<String, int>> optimizedSizes = {
    'blazeface': {'width': 96, 'height': 168}, // Reduced from 128x224
    'ultraface_rfb': {'width': 240, 'height': 180}, // Reduced from 320x240
    'ultraface_slim': {'width': 240, 'height': 180}, // Reduced from 320x240
  };
  
  // Relaxed confidence thresholds
  static const Map<String, double> optimizedThresholds = {
    'blazeface': 0.25,
    'ultraface_rfb': 0.3,
    'ultraface_slim': 0.3,
  };
  
  // Frame processing intervals
  static const Map<String, int> frameSkipIntervals = {
    'blazeface': 1, // Process every 2nd frame
    'ultraface_rfb': 3, // Process every 4th frame
    'ultraface_slim': 2, // Process every 3rd frame
  };
}
