// Working Face Detector - Simplified version to fix inference issues
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:image/image.dart' as img;
import 'package:tflite_flutter/tflite_flutter.dart';
import 'package:live_face_detection/models/recognition.dart';
import 'package:live_face_detection/service/optimized_yuv_converter.dart';

/// Simple working face detector to debug inference issues
class WorkingFaceDetector {
  static const String _modelPath = 'assets/civams_models/blazeface.tfl';
  
  // Use original BlazeFace input size to avoid shape issues
  static const int _inputWidth = 128;
  static const int _inputHeight = 224;
  
  Interpreter? _interpreter;
  bool _isInitialized = false;
  bool _isProcessing = false;
  
  /// Initialize face detector
  Future<bool> initialize() async {
    try {
      print('🔄 Loading BlazeFace model (working version)...');
      
      // Use simple CPU configuration
      final options = InterpreterOptions();
      options.threads = 2; // Use fewer threads to avoid issues
      
      _interpreter = await Interpreter.fromAsset(_modelPath, options: options);
      
      // Debug model info
      _debugModelInfo();
      
      _isInitialized = true;
      print('✅ Working face detector initialized');
      return true;
    } catch (e) {
      print('❌ Failed to initialize working detector: $e');
      return false;
    }
  }
  
  /// Debug model information
  void _debugModelInfo() {
    if (_interpreter == null) return;
    
    try {
      print('\n🔍 BLAZEFACE MODEL DEBUG INFO:');
      
      final inputTensors = _interpreter!.getInputTensors();
      final outputTensors = _interpreter!.getOutputTensors();
      
      print('Input tensors: ${inputTensors.length}');
      for (int i = 0; i < inputTensors.length; i++) {
        final tensor = inputTensors[i];
        print('  Input $i: shape=${tensor.shape}, type=${tensor.type}');
      }
      
      print('Output tensors: ${outputTensors.length}');
      for (int i = 0; i < outputTensors.length; i++) {
        final tensor = outputTensors[i];
        print('  Output $i: shape=${tensor.shape}, type=${tensor.type}');
      }
      
      print('Expected input: [1, $_inputHeight, $_inputWidth, 3]');
      
    } catch (e) {
      print('❌ Error debugging model info: $e');
    }
  }
  
  /// Detect faces with simplified approach
  Future<List<Recognition>> detectFaces(CameraImage cameraImage) async {
    if (!_isInitialized || _interpreter == null || _isProcessing) {
      return [];
    }
    
    _isProcessing = true;
    final totalStopwatch = Stopwatch()..start();
    
    try {
      print('🔄 Processing frame: ${cameraImage.width}x${cameraImage.height}');
      
      // Step 1: Convert to RGB
      final rgbImage = OptimizedYuvConverter.convertYuv420ToRgbOptimized(cameraImage);
      
      // Step 2: Resize to model input size
      final resized = img.copyResize(rgbImage, width: _inputWidth, height: _inputHeight);
      print('🔄 Resized to: ${_inputWidth}x${_inputHeight}');
      
      // Step 3: Create input tensor
      final input = _createInputTensor(resized);
      print('🔄 Created input tensor: ${_getInputShape(input)}');
      
      // Step 4: Run inference
      final inferenceStopwatch = Stopwatch()..start();
      final success = await _runSimpleInference(input);
      inferenceStopwatch.stop();
      
      totalStopwatch.stop();
      
      print('🎯 Inference result: ${success ? 'SUCCESS' : 'FAILED'} in ${inferenceStopwatch.elapsedMilliseconds}ms');
      print('🎯 Total processing: ${totalStopwatch.elapsedMilliseconds}ms');
      
      // For now, return empty list but log success
      return [];
      
    } catch (e) {
      print('❌ Error in face detection: $e');
      return [];
    } finally {
      _isProcessing = false;
    }
  }
  
  /// Create input tensor from image
  List<List<List<List<double>>>> _createInputTensor(img.Image image) {
    final input = List.generate(1, (_) =>
      List.generate(_inputHeight, (y) =>
        List.generate(_inputWidth, (x) =>
          List.generate(3, (c) {
            final pixel = image.getPixel(x, y);
            switch (c) {
              case 0: return pixel.r / 255.0; // Red
              case 1: return pixel.g / 255.0; // Green  
              case 2: return pixel.b / 255.0; // Blue
              default: return 0.0;
            }
          })
        )
      )
    );
    return input;
  }
  
  /// Get input shape for debugging
  List<int> _getInputShape(List<List<List<List<double>>>> input) {
    return [
      input.length,
      input[0].length,
      input[0][0].length,
      input[0][0][0].length,
    ];
  }
  
  /// Run simple inference to test if model works
  Future<bool> _runSimpleInference(List<List<List<List<double>>>> input) async {
    try {
      print('🔄 Starting inference...');
      
      // Method 1: Try runForMultipleInputs
      try {
        final outputs = <int, Object>{};
        _interpreter!.runForMultipleInputs([input], outputs);
        print('✅ runForMultipleInputs succeeded');
        return true;
      } catch (e) {
        print('❌ runForMultipleInputs failed: $e');
      }
      
      // Method 2: Try allocateTensors + run
      try {
        _interpreter!.allocateTensors();
        
        // Get input tensor and copy data
        final inputTensor = _interpreter!.getInputTensor(0);
        print('🔍 Input tensor shape: ${inputTensor.shape}');
        
        // Convert input to flat list
        final flatInput = _flattenInput(input);
        print('🔍 Flat input length: ${flatInput.length}');
        
        // Copy data (this might fail due to API differences)
        // inputTensor.copyFrom(flatInput);
        
        _interpreter!.invoke();
        print('✅ allocateTensors + invoke succeeded');
        return true;
      } catch (e) {
        print('❌ allocateTensors + invoke failed: $e');
      }
      
      return false;
      
    } catch (e) {
      print('❌ All inference methods failed: $e');
      return false;
    }
  }
  
  /// Flatten 4D input to 1D for tensor copying
  List<double> _flattenInput(List<List<List<List<double>>>> input) {
    final flat = <double>[];
    for (final batch in input) {
      for (final row in batch) {
        for (final col in row) {
          for (final channel in col) {
            flat.add(channel);
          }
        }
      }
    }
    return flat;
  }
  
  /// Test model with dummy data
  Future<bool> testModel() async {
    if (!_isInitialized || _interpreter == null) {
      print('❌ Model not initialized');
      return false;
    }
    
    try {
      print('🧪 Testing model with dummy data...');
      
      // Create dummy input with correct shape
      final dummyInput = List.generate(1, (_) =>
        List.generate(_inputHeight, (_) =>
          List.generate(_inputWidth, (_) =>
            List.generate(3, (_) => 0.5) // Dummy value
          )
        )
      );
      
      final success = await _runSimpleInference(dummyInput);
      
      if (success) {
        print('✅ Model test PASSED');
      } else {
        print('❌ Model test FAILED');
      }
      
      return success;
      
    } catch (e) {
      print('❌ Model test error: $e');
      return false;
    }
  }
  
  /// Dispose resources
  void dispose() {
    _interpreter?.close();
    _isInitialized = false;
  }
}
