# 🧠 C<PERSON>VAMS Face Terminal - AI Models Collection

## 📋 Overview
This directory contains all AI models extracted from C<PERSON><PERSON>MS Face Terminal APK, analyzed and documented for research and educational purposes.

## 📁 Directory Structure
```
civams_models/
├── bundled_models/          # Core application models
├── mlkit_models/           # Google MLKit barcode models  
├── other_models/           # Additional models (if any)
├── README.md              # This documentation
├── models_info.json       # Detailed model information
└── usage_examples.md      # Usage examples and guides
```

## 🎯 Models by Purpose

### 👤 Face Detection & Analysis

**BCLjoy_200.emd**
- Purpose: Face analysis model (BCL format)
- Size: 0.02 MB
- Format: .emd
- Input: unknown
- Output: unknown
- Usage: Proprietary format for face analysis

**BCLlefteyeclosed_200.emd**
- Purpose: Eye open/closed state detection
- Size: 0.01 MB
- Format: .emd
- Input: Eye region (typically 32x32)
- Output: Open/closed probability
- Usage: Detect if eyes are open or closed for liveness detection

**BCLrighteyeclosed_200.emd**
- Purpose: Eye open/closed state detection
- Size: 0.01 MB
- Format: .emd
- Input: Eye region (typically 32x32)
- Output: Open/closed probability
- Usage: Detect if eyes are open or closed for liveness detection

**blazeface.tfl**
- Purpose: Google BlazeFace model for real-time face detection
- Size: 0.33 MB
- Format: .tfl
- Input: 128x128x3 or 256x256x3
- Output: Face bounding boxes + landmarks
- Usage: Use for fast face detection in real-time applications

**contours.tfl**
- Purpose: Face contour and landmark detection model
- Size: 1.07 MB
- Format: .tfl
- Input: Face region (typically 192x192x3)
- Output: 468 face landmarks (x,y coordinates)
- Usage: Apply after face detection to get detailed facial landmarks

**LMprec_600.emd**
- Purpose: High-precision landmark detection model
- Size: 1.03 MB
- Format: .emd
- Input: Face region
- Output: Precise facial landmarks
- Usage: Get high-precision facial landmarks for face alignment

### 📊 Barcode & QR Code Detection

**barcode_ssd_mobilenet_v1_dmp25_quant.tflite**
- Purpose: Barcode and QR code detection model
- Size: 0.37 MB
- Format: .tflite
- Usage: Detect barcodes/QR codes in images before decoding

**oned_auto_regressor_mobile.tflite**
- Purpose: 1D barcode auto-regressor for decoding
- Size: 0.20 MB
- Format: .tflite
- Usage: Decode 1D barcodes from extracted features

**oned_feature_extractor_mobile.tflite**
- Purpose: 1D barcode feature extraction model
- Size: 0.26 MB
- Format: .tflite
- Usage: Extract features from detected 1D barcodes

### 🔧 Other Models

**fssd_25_8bit_gray_v2.tflite**
- Purpose: Feature-fused Single Shot Detector for object detection
- Size: 0.22 MB
- Format: .tflite

**fssd_25_8bit_v2.tflite**
- Purpose: Feature-fused Single Shot Detector for object detection
- Size: 0.22 MB
- Format: .tflite

**fssd_anchors_v2.pb**
- Purpose: Feature-fused Single Shot Detector for object detection
- Size: 0.00 MB
- Format: .pb

**fssd_anchors_v5.pb**
- Purpose: Feature-fused Single Shot Detector for object detection
- Size: 0.00 MB
- Format: .pb

**fssd_medium_8bit_gray_v5.tflite**
- Purpose: Feature-fused Single Shot Detector for object detection
- Size: 0.58 MB
- Format: .tflite

**fssd_medium_8bit_v5.tflite**
- Purpose: Feature-fused Single Shot Detector for object detection
- Size: 0.58 MB
- Format: .tflite

**MFT_fssd_accgray.pb**
- Purpose: Feature-fused Single Shot Detector for object detection
- Size: 0.00 MB
- Format: .pb

**MFT_fssd_fastgray.pb**
- Purpose: Feature-fused Single Shot Detector for object detection
- Size: 0.00 MB
- Format: .pb

## 📊 Summary Statistics
- **Total Models**: 17
- **Total Size**: 4.92 MB
- **Face Models**: 6
- **Barcode Models**: 3
- **Other Models**: 8

## 🚀 Quick Start Guide
1. Choose the appropriate model for your use case
2. Check the input/output specifications
3. Follow the usage examples in `usage_examples.md`
4. Integrate with TensorFlow Lite in your application

## ⚠️ Important Notes
- These models are extracted for research and educational purposes
- Check licensing requirements before commercial use
- Some models may require specific preprocessing
- Performance may vary on different hardware

## 🔗 Related Documentation
- [Usage Examples](usage_examples.md)
- [Model Details](models_info.json)
- [CIVAMS Analysis Report](../CIVAMS_vs_ArcFace_Analysis_Report.md)
