// Simple Detector Comparison - BlazeFace vs MediaPipe
import 'package:camera/camera.dart';
import 'package:live_face_detection/service/simple_face_detector.dart';
import 'package:live_face_detection/service/simple_mediapipe_detector.dart';

/// Simple comparison between BlazeFace and MediaPipe
class SimpleDetectorComparison {
  SimpleFaceDetector? _blazeFaceDetector;
  SimpleMediaPipeDetector? _mediaPipeDetector;
  
  bool _isInitialized = false;
  String _currentDetector = 'BlazeFace';
  
  /// Initialize both detectors
  Future<bool> initialize() async {
    try {
      print('🔄 Initializing detector comparison...');
      
      // Initialize BlazeFace
      _blazeFaceDetector = SimpleFaceDetector();
      final blazeSuccess = await _blazeFaceDetector!.initialize();
      
      // Initialize MediaPipe
      _mediaPipeDetector = SimpleMediaPipeDetector();
      final mediaSuccess = await _mediaPipeDetector!.initialize();
      
      _isInitialized = blazeSuccess && mediaSuccess;
      
      print('📊 Detector Status:');
      print('  BlazeFace: ${blazeSuccess ? 'OK' : 'FAILED'}');
      print('  MediaPipe: ${mediaSuccess ? 'OK' : 'FAILED'}');
      
      return _isInitialized;
    } catch (e) {
      print('❌ Comparison init error: $e');
      return false;
    }
  }
  
  /// Run comprehensive comparison
  Future<void> runComparison(CameraImage cameraImage) async {
    if (!_isInitialized) {
      print('❌ Detectors not initialized');
      return;
    }
    
    print('\n🏁 DETECTOR PERFORMANCE COMPARISON');
    print('=' * 50);
    print('Image size: ${cameraImage.width}x${cameraImage.height}');
    
    // Test BlazeFace
    if (_blazeFaceDetector != null) {
      print('\n🔥 Testing BlazeFace...');
      
      final blazeTimes = <int>[];
      final blazeDetections = <int>[];
      
      for (int i = 0; i < 10; i++) {
        final stopwatch = Stopwatch()..start();
        final results = await _blazeFaceDetector!.detectFaces(cameraImage);
        stopwatch.stop();
        
        blazeTimes.add(stopwatch.elapsedMilliseconds);
        blazeDetections.add(results.length);
        
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      final blazeAvg = blazeTimes.reduce((a, b) => a + b) / blazeTimes.length;
      final blazeDetAvg = blazeDetections.reduce((a, b) => a + b) / blazeDetections.length;
      
      print('  Average Time: ${blazeAvg.round()}ms');
      print('  Average FPS: ${(1000 / blazeAvg).toStringAsFixed(1)}');
      print('  Average Detections: ${blazeDetAvg.toStringAsFixed(1)}');
      print('  Times: $blazeTimes');
    }
    
    // Test MediaPipe
    if (_mediaPipeDetector != null) {
      print('\n🎯 Testing MediaPipe...');
      
      final mediaTimes = <int>[];
      final mediaDetections = <int>[];
      
      for (int i = 0; i < 10; i++) {
        final stopwatch = Stopwatch()..start();
        final count = await _mediaPipeDetector!.detectFaceCount(cameraImage);
        stopwatch.stop();
        
        mediaTimes.add(stopwatch.elapsedMilliseconds);
        mediaDetections.add(count);
        
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      final mediaAvg = mediaTimes.reduce((a, b) => a + b) / mediaTimes.length;
      final mediaDetAvg = mediaDetections.reduce((a, b) => a + b) / mediaDetections.length;
      
      print('  Average Time: ${mediaAvg.round()}ms');
      print('  Average FPS: ${(1000 / mediaAvg).toStringAsFixed(1)}');
      print('  Average Detections: ${mediaDetAvg.toStringAsFixed(1)}');
      print('  Times: $mediaTimes');
      
      // Comparison
      if (_blazeFaceDetector != null) {
        final blazeAvg = blazeTimes.reduce((a, b) => a + b) / blazeTimes.length;
        
        print('\n🏆 COMPARISON RESULTS:');
        if (blazeAvg < mediaAvg) {
          final speedup = (mediaAvg / blazeAvg).toStringAsFixed(1);
          print('  Winner: BlazeFace (${speedup}x faster)');
        } else if (mediaAvg < blazeAvg) {
          final speedup = (blazeAvg / mediaAvg).toStringAsFixed(1);
          print('  Winner: MediaPipe (${speedup}x faster)');
        } else {
          print('  Result: Similar performance');
        }
        
        print('  BlazeFace: ${blazeAvg.round()}ms avg');
        print('  MediaPipe: ${mediaAvg.round()}ms avg');
        print('  Difference: ${(blazeAvg - mediaAvg).abs().round()}ms');
      }
    }
    
    print('=' * 50);
  }
  
  /// Switch detector for UI
  void switchDetector() {
    _currentDetector = _currentDetector == 'BlazeFace' ? 'MediaPipe' : 'BlazeFace';
    print('🔄 Switched to $_currentDetector');
  }
  
  /// Get current detector name
  String get currentDetector => _currentDetector;
  
  /// Quick performance test
  Future<Map<String, int>> quickTest(CameraImage cameraImage) async {
    if (!_isInitialized) return {};
    
    final results = <String, int>{};
    
    // Test BlazeFace
    if (_blazeFaceDetector != null) {
      final stopwatch = Stopwatch()..start();
      await _blazeFaceDetector!.detectFaces(cameraImage);
      stopwatch.stop();
      results['BlazeFace'] = stopwatch.elapsedMilliseconds;
    }
    
    // Test MediaPipe
    if (_mediaPipeDetector != null) {
      final stopwatch = Stopwatch()..start();
      await _mediaPipeDetector!.detectFaceCount(cameraImage);
      stopwatch.stop();
      results['MediaPipe'] = stopwatch.elapsedMilliseconds;
    }
    
    return results;
  }
  
  /// Dispose resources
  void dispose() {
    _blazeFaceDetector?.dispose();
    _mediaPipeDetector?.dispose();
    _isInitialized = false;
  }
}
