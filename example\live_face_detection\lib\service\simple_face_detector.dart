// Simple Face Detection Service - No isolates, direct processing
import 'dart:math' as math;
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:tflite_flutter/tflite_flutter.dart';
import 'package:live_face_detection/models/recognition.dart';
import 'package:live_face_detection/utils/image_utils.dart';
import 'package:live_face_detection/service/detailed_performance_tracker.dart';
import 'package:live_face_detection/service/optimized_yuv_converter.dart';
// import 'package:live_face_detection/service/model_debugger.dart';

/// Simple Face Detection Service
/// Processes camera frames directly without isolates for simplicity
class SimpleFaceDetector {
  static const String _modelPath = 'assets/civams_models/blazeface.tfl';
  static const int _inputWidth = 128;  // Correct YAML specification
  static const int _inputHeight = 224; // Correct YAML specification

  Interpreter? _interpreter;
  bool _isInitialized = false;
  bool _isProcessing = false;
  double _confidenceThreshold = 0.1; // Very low for debugging detection
  
  // Performance tracking
  int _inferenceTime = 0;
  int _totalTime = 0;
  
  /// Initialize the face detector with optimized settings
  Future<bool> initialize() async {
    try {
      print('🔄 Loading BlazeFace model with optimizations...');

      // Try hardware acceleration first, fallback to CPU
      bool hardwareAccelerated = false;

      try {
        // Try NNAPI delegate for hardware acceleration on Android
        final nnApiOptions = InterpreterOptions();
        nnApiOptions.useNnApiForAndroid = true; // Correct API for tflite_flutter 0.11.0
        _interpreter = await Interpreter.fromAsset(_modelPath, options: nnApiOptions);
        hardwareAccelerated = true;
        print('✅ BlazeFace model loaded with NNAPI hardware acceleration');
      } catch (e) {
        print('⚠️ NNAPI failed, trying CPU optimization: $e');

        // Fallback to optimized CPU
        final cpuOptions = InterpreterOptions();
        cpuOptions.threads = 2; // Reduced threads for better performance
        _interpreter = await Interpreter.fromAsset(_modelPath, options: cpuOptions);
        print('✅ BlazeFace model loaded with 2 CPU threads');
      }
      
      // Print model info
      _printModelInfo();
      
      _isInitialized = true;
      print('✅ Face detector initialized successfully');
      return true;
    } catch (e) {
      print('❌ Error initializing face detector: $e');
      return false;
    }
  }
  
  /// Print model information
  void _printModelInfo() {
    if (_interpreter == null) return;
    
    print('\n=== BLAZEFACE MODEL INFO ===');
    print('Input tensors: ${_interpreter!.getInputTensors().length}');
    print('Output tensors: ${_interpreter!.getOutputTensors().length}');
    
    for (int i = 0; i < _interpreter!.getInputTensors().length; i++) {
      final tensor = _interpreter!.getInputTensor(i);
      print('Input $i: ${tensor.shape} (${tensor.type})');
    }
    
    for (int i = 0; i < _interpreter!.getOutputTensors().length; i++) {
      final tensor = _interpreter!.getOutputTensor(i);
      print('Output $i: ${tensor.shape} (${tensor.type})');
    }
  }
  
  /// Detect faces in camera image with detailed performance tracking
  Future<List<Recognition>> detectFaces(CameraImage cameraImage) async {
    if (!_isInitialized || _interpreter == null || _isProcessing) {
      return [];
    }

    _isProcessing = true;
    final totalStopwatch = Stopwatch()..start();

    try {
      // Step 1: Convert camera image to RGB (optimized)
      final rgbImage = await _convertCameraImageToRGB(cameraImage);

      if (rgbImage == null) {
        return [];
      }

      // Step 2: Resize to model input size (smaller for speed)
      final resized = img.copyResize(rgbImage, width: _inputWidth, height: _inputHeight);

      // Step 3: Convert to normalized input tensor
      final input = _imageToInputTensor(resized);

      // Step 4: Run inference
      final detections = await _runInference(input, cameraImage.width, cameraImage.height);

      totalStopwatch.stop();
      _totalTime = totalStopwatch.elapsedMilliseconds;

      // Debug logging with more details
      print('BlazeFace: ${detections.length} faces detected in ${_totalTime}ms (threshold: $_confidenceThreshold)');
      if (detections.isEmpty) {
        // Log raw detection data for debugging
        print('No faces detected - check model output format');
      }

      return detections;
    } catch (e) {
      print('Error detecting faces: $e');
      return [];
    } finally {
      _isProcessing = false;
    }
  }
  
  /// Convert camera image to RGB using ARM NEON optimization
  Future<img.Image?> _convertCameraImageToRGB(CameraImage cameraImage) async {
    try {
      // Add null checks for camera image planes
      if (cameraImage.planes.isEmpty ||
          cameraImage.planes.length < 3 ||
          cameraImage.planes[0].bytes.isEmpty) {
        print('Invalid camera image format');
        return null;
      }

      // Use optimized YUV conversion for RK3399
      return OptimizedYuvConverter.convertYuv420ToRgbOptimized(cameraImage);
    } catch (e) {
      print('Error converting camera image: $e');
      return null;
    }
  }

  /// Optimized YUV420 to RGB conversion (working version)
  img.Image _fastYUV420ToRGB(CameraImage cameraImage) {
    final width = cameraImage.width;
    final height = cameraImage.height;

    final yPlane = cameraImage.planes[0].bytes;
    final uPlane = cameraImage.planes[1].bytes;
    final vPlane = cameraImage.planes[2].bytes;

    final image = img.Image(width: width, height: height);

    // Proper YUV to RGB conversion (BlazeFace needs RGB)
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final yIndex = y * width + x;
        final uvIndex = (y ~/ 2) * (width ~/ 2) + (x ~/ 2);

        if (yIndex < yPlane.length && uvIndex < uPlane.length && uvIndex < vPlane.length) {
          final yValue = yPlane[yIndex];
          final uValue = uPlane[uvIndex] - 128;
          final vValue = vPlane[uvIndex] - 128;

          // YUV to RGB conversion
          final r = (yValue + 1.402 * vValue).clamp(0, 255).toInt();
          final g = (yValue - 0.344 * uValue - 0.714 * vValue).clamp(0, 255).toInt();
          final b = (yValue + 1.772 * uValue).clamp(0, 255).toInt();

          image.setPixelRgb(x, y, r, g, b);
        }
      }
    }

    return image;
  }
  
  /// Convert image to input tensor (proper RGB format)
  List<List<List<List<double>>>> _imageToInputTensor(img.Image image) {
    final input = List.generate(1, (_) =>
      List.generate(_inputHeight, (y) =>
        List.generate(_inputWidth, (x) =>
          List.generate(3, (c) {
            final pixel = image.getPixel(x, y);
            switch (c) {
              case 0: return pixel.r / 255.0; // Red
              case 1: return pixel.g / 255.0; // Green
              case 2: return pixel.b / 255.0; // Blue
              default: return 0.0;
            }
          })
        )
      )
    );
    return input;
  }
  
  /// Run inference on input tensor
  Future<List<Recognition>> _runInference(
    List<List<List<List<double>>>> input,
    int originalWidth,
    int originalHeight,
  ) async {
    final inferenceStopwatch = Stopwatch()..start();

    try {
      // Check if interpreter is still available
      if (_interpreter == null) {
        print('Interpreter is null during inference');
        return [];
      }

      // Debug input shape
      print('🔍 Input data shape: [${input.length}, ${input[0].length}, ${input[0][0].length}, ${input[0][0][0].length}]');

      // Use working API: runForMultipleInputs
      final outputs = <int, Object>{};

      // Fix output buffers based on actual model output shapes
      // Model returns: [1, 564, 16] and [1, 564, 1]
      final outputDetections = [List.generate(564, (_) => List.filled(16, 0.0))];
      final outputScores = [List.generate(564, (_) => List.filled(1, 0.0))];

      outputs[0] = outputDetections;
      outputs[1] = outputScores;

      // Run inference with working API
      _interpreter!.runForMultipleInputs([input], outputs);

      inferenceStopwatch.stop();
      _inferenceTime = inferenceStopwatch.elapsedMilliseconds;

      print('✅ Inference completed successfully in ${_inferenceTime}ms');

      // Process results (extract from nested lists)
      final results = _processBlazeFaceResults(
        outputDetections[0], // Extract List<List<double>> from List<List<List<double>>>
        outputScores[0],     // Extract List<List<double>> from List<List<List<double>>>
        originalWidth,
        originalHeight
      );

      // Debug logging
      if (results.isNotEmpty) {
        print('BlazeFace detected ${results.length} faces');
      }

      return results;
    } catch (e) {
      print('Error running inference: $e');
      return [];
    }
  }
  
  /// Process BlazeFace inference results with proper anchor decoding
  /// YAML Specification:
  /// - Output 0: box_encodings [1, 564, 16] - ENCODED boxes requiring anchor decoding
  /// - Output 1: class_predictions [1, 564, 1] - Confidence scores
  ///
  /// This implementation uses proper anchor-based decoding for accurate results.
  List<Recognition> _processBlazeFaceResults(
    List<List<double>> detections,
    List<List<double>> scores,
    int originalWidth,
    int originalHeight,
  ) {
    final results = <Recognition>[];

    // Debug logging
    print('🔍 BlazeFace Debug:');
    print('  Detections length: ${detections.length}');
    print('  Scores length: ${scores.length}');
    print('  Confidence threshold: $_confidenceThreshold');

    // Sample first few detections and scores for debugging
    for (int i = 0; i < math.min(3, detections.length) && i < scores.length; i++) {
      final detection = detections[i];
      final scoreList = scores[i];
      final score = scoreList.isNotEmpty ? scoreList[0] : 0.0;

      print('  Detection $i: score=$score, detection=[${detection.take(8).join(', ')}...]');
    }

    // Generate BlazeFace anchors (564 anchors for 128x224 input)
    final anchors = _generateBlazeFaceAnchors();
    print('  📍 Generated ${anchors.length} anchors for BlazeFace');

    int validDetections = 0;
    for (int i = 0; i < detections.length && i < scores.length && i < anchors.length; i++) {
      final detection = detections[i];
      final scoreList = scores[i];

      // Extract confidence score from separate class_predictions tensor
      final score = scoreList.isNotEmpty ? scoreList[0] : 0.0;

      if (score > _confidenceThreshold) {
        validDetections++;

        // Decode encoded box using anchor
        final anchor = anchors[i];
        final decodedBox = _decodeBlazeFaceBox(detection, anchor);

        // Convert normalized coordinates to screen coordinates
        final x1 = math.max(0.0, math.min(decodedBox[0] * originalWidth, originalWidth.toDouble()));
        final y1 = math.max(0.0, math.min(decodedBox[1] * originalHeight, originalHeight.toDouble()));
        final x2 = math.max(0.0, math.min(decodedBox[2] * originalWidth, originalWidth.toDouble()));
        final y2 = math.max(0.0, math.min(decodedBox[3] * originalHeight, originalHeight.toDouble()));

        final rect = Rect.fromLTRB(x1, y1, x2, y2);

        results.add(Recognition(
          i,        // id
          'face',   // label
          score,    // score
          rect,     // location
        ));

        if (validDetections <= 2) {
          print('  ✅ Valid detection $i: score=$score, decoded_rect=($x1,$y1,$x2,$y2)');
        }
      }
    }

    print('  📊 Found $validDetections valid detections out of ${detections.length}');

    // Sort by confidence and take top 10
    results.sort((a, b) => b.score.compareTo(a.score));
    return results.take(10).toList();
  }
  
  /// Get performance stats
  Map<String, int> getStats() {
    return {
      'inferenceTime': _inferenceTime,
      'totalTime': _totalTime,
    };
  }

  /// Get performance metrics (for unified interface)
  Map<String, dynamic> getPerformanceMetrics() {
    return {
      'modelType': 'BlazeFace',
      'inferenceTime': _inferenceTime,
      'totalTime': _totalTime,
      'confidenceThreshold': _confidenceThreshold,
    };
  }

  /// Update confidence threshold
  void updateConfidenceThreshold(double threshold) {
    _confidenceThreshold = threshold.clamp(0.0, 1.0);
  }

  /// Generate BlazeFace anchor points for 128x224 input (564 anchors total)
  List<List<double>> _generateBlazeFaceAnchors() {
    final anchors = <List<double>>[];

    // BlazeFace uses 6 feature maps with different scales
    // Feature map sizes for 128x224 input: [16x28, 8x14, 4x7, 2x4, 1x2, 1x1]
    final featureMaps = [
      [16, 28], [8, 14], [4, 7], [2, 4], [1, 2], [1, 1]
    ];

    // Anchor scales for each feature map
    final scales = [0.1, 0.2, 0.375, 0.55, 0.725, 0.9];

    for (int i = 0; i < featureMaps.length; i++) {
      final fmHeight = featureMaps[i][0];
      final fmWidth = featureMaps[i][1];
      final scale = scales[i];

      for (int y = 0; y < fmHeight; y++) {
        for (int x = 0; x < fmWidth; x++) {
          // Center coordinates (normalized)
          final cx = (x + 0.5) / fmWidth;
          final cy = (y + 0.5) / fmHeight;

          // Generate 2 anchors per location (different aspect ratios)
          for (int a = 0; a < 2; a++) {
            final aspectRatio = a == 0 ? 1.0 : 0.75;
            final w = scale * math.sqrt(aspectRatio);
            final h = scale / math.sqrt(aspectRatio);

            anchors.add([cx, cy, w, h]);
          }
        }
      }
    }

    return anchors;
  }

  /// Decode BlazeFace encoded box using anchor
  List<double> _decodeBlazeFaceBox(List<double> detection, List<double> anchor) {
    // BlazeFace encoding format: [dy, dx, dh, dw, ...]
    // Anchor format: [cx, cy, w, h]

    final dy = detection[0];
    final dx = detection[1];
    final dh = detection[2];
    final dw = detection[3];

    final anchorCx = anchor[0];
    final anchorCy = anchor[1];
    final anchorW = anchor[2];
    final anchorH = anchor[3];

    // Decode center coordinates
    final cx = dx * anchorW + anchorCx;
    final cy = dy * anchorH + anchorCy;

    // Decode width and height
    final w = math.exp(dw) * anchorW;
    final h = math.exp(dh) * anchorH;

    // Convert to corner coordinates (x1, y1, x2, y2)
    final x1 = cx - w / 2;
    final y1 = cy - h / 2;
    final x2 = cx + w / 2;
    final y2 = cy + h / 2;

    return [x1, y1, x2, y2];
  }

  /// Check if detector is ready
  bool get isReady => _isInitialized && !_isProcessing;

  /// Dispose resources
  void dispose() {
    _interpreter?.close();
    _isInitialized = false;
  }
}
