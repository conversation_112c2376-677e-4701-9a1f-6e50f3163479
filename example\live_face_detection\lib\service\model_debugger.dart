// Model Debugger - Debug TensorFlow Lite model input/output requirements
import 'package:tflite_flutter/tflite_flutter.dart';

class ModelDebugger {
  /// Debug model input/output specifications
  static Future<void> debugModelSpecs(String modelPath) async {
    try {
      print('\n🔍 DEBUGGING MODEL SPECIFICATIONS');
      print('Model: $modelPath');
      
      // Load interpreter
      final interpreter = await Interpreter.fromAsset(modelPath);
      
      // Get input details
      final inputTensors = interpreter.getInputTensors();
      print('\n📥 INPUT TENSORS:');
      for (int i = 0; i < inputTensors.length; i++) {
        final tensor = inputTensors[i];
        print('  Input $i:');
        print('    Shape: ${tensor.shape}');
        print('    Type: ${tensor.type}');
        print('    Name: ${tensor.name}');
      }
      
      // Get output details
      final outputTensors = interpreter.getOutputTensors();
      print('\n📤 OUTPUT TENSORS:');
      for (int i = 0; i < outputTensors.length; i++) {
        final tensor = outputTensors[i];
        print('  Output $i:');
        print('    Shape: ${tensor.shape}');
        print('    Type: ${tensor.type}');
        print('    Name: ${tensor.name}');
      }
      
      // Test with correct input shape
      await _testModelInference(interpreter);
      
      interpreter.close();
      
    } catch (e) {
      print('❌ Model debug failed: $e');
    }
  }
  
  /// Test model inference with correct input
  static Future<void> _testModelInference(Interpreter interpreter) async {
    try {
      print('\n🧪 TESTING MODEL INFERENCE:');
      
      final inputTensors = interpreter.getInputTensors();
      if (inputTensors.isEmpty) {
        print('❌ No input tensors found');
        return;
      }
      
      final inputTensor = inputTensors[0];
      final inputShape = inputTensor.shape;
      
      print('Expected input shape: $inputShape');
      
      // Create test input with correct shape
      final testInput = _createTestInput(inputShape);
      
      print('Created test input shape: ${_getInputShape(testInput)}');
      
      // Set input
      interpreter.setInputTensor(0, testInput);
      print('✅ Input tensor set successfully');
      
      // Run inference
      interpreter.invoke();
      print('✅ Inference completed successfully');
      
      // Get outputs
      final outputTensors = interpreter.getOutputTensors();
      for (int i = 0; i < outputTensors.length; i++) {
        final output = interpreter.getOutputTensor(i);
        print('Output $i shape: ${_getOutputShape(output)}');
      }
      
    } catch (e) {
      print('❌ Test inference failed: $e');
    }
  }
  
  /// Create test input with correct shape
  static dynamic _createTestInput(List<int> shape) {
    if (shape.length == 4) {
      // 4D tensor: [batch, height, width, channels]
      final batch = shape[0];
      final height = shape[1];
      final width = shape[2];
      final channels = shape[3];
      
      return List.generate(batch, (_) =>
        List.generate(height, (_) =>
          List.generate(width, (_) =>
            List.generate(channels, (_) => 0.5) // Test value
          )
        )
      );
    } else if (shape.length == 3) {
      // 3D tensor: [height, width, channels]
      final height = shape[0];
      final width = shape[1];
      final channels = shape[2];
      
      return List.generate(height, (_) =>
        List.generate(width, (_) =>
          List.generate(channels, (_) => 0.5) // Test value
        )
      );
    } else {
      throw Exception('Unsupported input shape: $shape');
    }
  }
  
  /// Get input shape for debugging
  static List<int> _getInputShape(dynamic input) {
    if (input is List) {
      final shape = <int>[input.length];
      dynamic current = input[0];
      while (current is List) {
        shape.add(current.length);
        current = current.length > 0 ? current[0] : null;
      }
      return shape;
    }
    return [];
  }
  
  /// Get output shape for debugging
  static List<int> _getOutputShape(dynamic output) {
    if (output is List) {
      final shape = <int>[output.length];
      dynamic current = output[0];
      while (current is List) {
        shape.add(current.length);
        current = current.length > 0 ? current[0] : null;
      }
      return shape;
    }
    return [];
  }
}
