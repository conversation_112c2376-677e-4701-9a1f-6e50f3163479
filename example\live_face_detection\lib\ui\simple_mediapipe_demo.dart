import 'package:flutter/material.dart';
import 'package:live_face_detection/ui/simple_mediapipe_camera_widget.dart';

/// Demo page for Simple MediaPipe Face Detection with Bounding Boxes
class SimpleMediaPipeDemo extends StatelessWidget {
  const SimpleMediaPipeDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Row(
          children: [
            Image.asset(
              'assets/images/tfl_logo.png',
              fit: BoxFit.contain,
              height: 32,
            ),
            const SizedBox(width: 12),
            const Text(
              'MediaPipe Face Detection',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: const SimpleMediaPipeCameraWidget(),
    );
  }
}
