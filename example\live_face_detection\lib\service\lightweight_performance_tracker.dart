// Lightweight Performance Tracker - Minimal overhead for FPS debugging
import 'dart:collection';

/// Minimal performance tracker to debug FPS issues
/// Focuses only on essential metrics with minimal overhead
class LightweightPerformanceTracker {
  // Simple counters - no DateTime objects to reduce overhead
  static int _cameraFrameCount = 0;
  static int _processedFrameCount = 0;
  static int _lastReportTime = 0;
  
  // Minimal timing tracking
  static final Queue<int> _recentInferenceTimes = Queue<int>();
  static int _totalInferenceTime = 0;
  
  /// Record camera frame (minimal overhead)
  static void recordCameraFrame() {
    _cameraFrameCount++;
  }
  
  /// Record processed frame (minimal overhead)
  static void recordProcessedFrame() {
    _processedFrameCount++;
  }
  
  /// Record inference time (minimal overhead)
  static void recordInferenceTime(int timeMs) {
    _recentInferenceTimes.add(timeMs);
    _totalInferenceTime += timeMs;
    
    // Keep only recent 10 samples
    if (_recentInferenceTimes.length > 10) {
      final removed = _recentInferenceTimes.removeFirst();
      _totalInferenceTime -= removed;
    }
  }
  
  /// Get current FPS (minimal calculation)
  static Map<String, double> getCurrentFPS() {
    final now = DateTime.now().millisecondsSinceEpoch;
    
    if (_lastReportTime == 0) {
      _lastReportTime = now;
      return {'cameraFPS': 0.0, 'processingFPS': 0.0};
    }
    
    final elapsed = (now - _lastReportTime) / 1000.0; // seconds
    
    if (elapsed < 1.0) {
      return {'cameraFPS': 0.0, 'processingFPS': 0.0};
    }
    
    final cameraFPS = _cameraFrameCount / elapsed;
    final processingFPS = _processedFrameCount / elapsed;
    
    // Reset counters
    _cameraFrameCount = 0;
    _processedFrameCount = 0;
    _lastReportTime = now;
    
    return {
      'cameraFPS': cameraFPS,
      'processingFPS': processingFPS,
    };
  }
  
  /// Get average inference time
  static double getAverageInferenceTime() {
    if (_recentInferenceTimes.isEmpty) return 0.0;
    return _totalInferenceTime / _recentInferenceTimes.length;
  }
  
  /// Print lightweight report
  static void printLightweightReport() {
    final fps = getCurrentFPS();
    final avgInference = getAverageInferenceTime();
    
    print('🚀 Lightweight Performance:');
    print('  Camera FPS: ${fps['cameraFPS']!.toStringAsFixed(1)}');
    print('  Processing FPS: ${fps['processingFPS']!.toStringAsFixed(1)}');
    print('  Avg Inference: ${avgInference.toStringAsFixed(0)}ms');
    print('  FPS Ratio: ${(fps['processingFPS']! / fps['cameraFPS']! * 100).toStringAsFixed(0)}%');
  }
  
  /// Start minimal reporting
  static void startLightweightReporting() {
    Stream.periodic(const Duration(seconds: 5)).listen((_) {
      printLightweightReport();
    });
  }
}
